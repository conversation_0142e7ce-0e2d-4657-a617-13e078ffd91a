import streamlit as st

import streamlit as st
import pandas as pd
import openai

st.title("Hello, Streamlit!")
st.write("This is a test page.")

# --- Setup ---
openai.api_key = "********************************************************************************************************************************************************************"
DATA_FILE = "fpt0705_New_Delhi-2001(Sheet1).csv"

# --- App UI ---
st.title("NLP-Powered Dashboard Prototype")

# Step 1: Ask for constituency
constituency = st.text_input("Enter Constituency Name:", "New Delhi")

if constituency.lower() == "new delhi":
    df = pd.read_csv(DATA_FILE)

    # Step 2: Analytics Metrics
    total_population = df['Total - Persons'].sum()
    total_males = df['Total - Males'].sum()
    total_females = df['Total - Females'].sum()
    sc_total = df['Scheduled Caste - Persons'].sum()
    st_total = df['Scheduled Tribe - Persons'].sum()
    gender_ratio = round((total_females / total_males) * 1000, 1)
    sc_percent = round((sc_total / total_population) * 100, 2)
    st_percent = round((st_total / total_population) * 100, 2)

    st.subheader("📊 Analytics Summary")
    st.write(f"**Total Population:** {total_population}")
    st.write(f"**Gender Ratio:** {gender_ratio} females per 1000 males")
    st.write(f"**Scheduled Caste %:** {sc_percent}%")
    st.write(f"**Scheduled Tribe %:** {st_percent}%")

    # Step 3: Ask for AI Summary
    if st.button("Generate AI Summary"):
        prompt = f"""
        Generate a summary for New Delhi constituency based on:
        - Total Population: {total_population}
        - Gender Ratio: {gender_ratio}
        - Scheduled Caste %: {sc_percent}
        - Scheduled Tribe %: {st_percent}
        """

        with st.spinner("Generating AI Summary..."):
            try:
                response = openai.ChatCompletion.create(
                    model="gpt-3.5-turbo",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=200
                )
                ai_summary = response['choices'][0]['message']['content']
            except openai.error.RateLimitError as e:
                ai_summary = "Quota exceeded. Please check OpenAI billing or try later."
            
            st.subheader("AI-Generated Summary")
            st.write(ai_summary)