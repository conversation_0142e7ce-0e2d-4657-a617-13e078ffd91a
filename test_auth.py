#!/usr/bin/env python3
"""
Test script to verify authentication setup
"""

import os
import sys
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported."""
    print("🧪 Testing imports...")
    
    try:
        import streamlit as st
        print("✅ Streamlit imported successfully")
    except ImportError as e:
        print(f"❌ Streamlit import failed: {e}")
        return False
    
    try:
        import auth
        print("✅ Auth module imported successfully")
    except ImportError as e:
        print(f"❌ Auth module import failed: {e}")
        return False
    
    return True

def test_files():
    """Test if all required files exist."""
    print("\n📁 Testing file existence...")
    
    required_files = [
        "login.html",
        "POC-10J-17.py", 
        "auth.py",
        "start_app.py"
    ]
    
    all_exist = True
    for file in required_files:
        if Path(file).exists():
            print(f"✅ {file} exists")
        else:
            print(f"❌ {file} missing")
            all_exist = False
    
    return all_exist

def test_auth_functions():
    """Test authentication functions."""
    print("\n🔐 Testing authentication functions...")
    
    try:
        from auth import check_authentication, get_user_info
        print("✅ Authentication functions imported")
        
        # Test check_authentication (should return False without session)
        result = check_authentication()
        print(f"✅ check_authentication() returned: {result}")
        
        # Test get_user_info
        user_info = get_user_info()
        print(f"✅ get_user_info() returned: {user_info}")
        
        return True
    except Exception as e:
        print(f"❌ Authentication function test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 50)
    print("🧪 Authentication Setup Test")
    print("=" * 50)
    
    # Change to script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    print(f"📂 Working directory: {os.getcwd()}")
    
    # Run tests
    tests = [
        ("Import Test", test_imports),
        ("File Test", test_files),
        ("Auth Function Test", test_auth_functions)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Authentication setup is ready to use.")
        print("\n🚀 To start the application, run:")
        print("   python start_app.py")
    else:
        print("⚠️  SOME TESTS FAILED!")
        print("❌ Please fix the issues above before using the authentication system.")
    print("=" * 50)

if __name__ == "__main__":
    main()
