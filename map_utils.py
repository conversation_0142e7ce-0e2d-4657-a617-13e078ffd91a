import pandas as pd
import folium
from geopy.geocoders import Nominatim
from time import sleep

def get_lat_lon(pincode, geolocator):
    try:
        location = geolocator.geocode(f"{pincode}, Delhi, India")
        if location:
            return pd.Series([location.latitude, location.longitude])
    except:
        pass
    return pd.Series([None, None])

def add_coordinates(df, geolocator):
    coords = df['PIN'].apply(lambda pin: get_lat_lon(pin, geolocator))
    df['lat'], df['lon'] = coords[0], coords[1]
    sleep(1)
    return df.dropna(subset=['lat', 'lon'])

def build_dealer_map():
    # Load data
    amaron_df = pd.read_csv('amaron_retailer_image.csv')
    exide_df = pd.read_csv('exide_retailer_image.csv')
    tata_df = pd.read_csv('tata_green_retailer_image.csv')

    geolocator = Nominatim(user_agent="dealer-mapper")
    amaron_df = add_coordinates(amaron_df, geolocator)
    exide_df = add_coordinates(exide_df, geolocator)
    tata_df = add_coordinates(tata_df, geolocator)

    # Map setup
    delhi_center = [28.6139, 77.2090]
    map_delhi = folium.Map(location=delhi_center, zoom_start=11)

    def add_markers(df, color, name):
        for _, row in df.iterrows():
            html = f"""
                <b>{name} Dealer:</b> {row['Name']}<br>
                <b>PIN:</b> {row['PIN']}<br>
                <img src='images/{row["image"]}' width='200' height='150'>
            """
            folium.Marker(
                location=(row['lat'], row['lon']),
                popup=folium.Popup(html, max_width=250),
                icon=folium.Icon(color=color, icon='info-sign')
            ).add_to(map_delhi)

    add_markers(amaron_df, 'red', 'Amaron')
    add_markers(exide_df, 'blue', 'Exide')
    add_markers(tata_df, 'green', 'Tata Green')

    folium.LayerControl().add_to(map_delhi)
    map_delhi.save('delhi_dealer_district_map_with_images-POC.html')
    print("Map with images saved as 'delhi_dealer_district_map_with_images POC.html'")

    return map_delhi._repr_html_()  # Return HTML string to embed in Streamlit
