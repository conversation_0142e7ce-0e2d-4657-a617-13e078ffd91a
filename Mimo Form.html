<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>KPI Form - GeoIQ</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="min-h-screen bg-gray-50 flex flex-col items-center relative">

  <!-- Header <PERSON>gos -->
  <div class="logo-header w-full flex justify-between items-center px-6 py-4 bg-white shadow-md z-50">
    <img src="logo/Adobe Express - file.png" alt="Stonesbury Logo" class="h-14 w-auto" />
  </div>

  <!-- Form Container -->
  <div class="form-container">
    <div class="w-full max-w-9xl bg-white rounded-lg p-8 mt-10" style="box-shadow: 0 8px 25px rgba(255, 102, 0, 0.4), 0 4px 15px rgba(255, 102, 0, 0.2); width: 90vw;">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">KPI Validation Form</h2>
    <form class="grid grid-cols-1 md:grid-cols-2 gap-6">

      <div>
        <label class="block text-sm font-medium text-gray-700">Service Provider <span class="text-red-500">*</span></label>
        <input type="text" value="Mimo" class="mt-1 w-full border rounded px-4 py-2" required/>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Service <span class="text-red-500">*</span></label>
        <input type="text" value="KPI Validation" class="mt-1 w-full border rounded px-4 py-2" required/>
      </div>

      <div class="md:col-span-2">
        <label class="block text-sm font-medium text-gray-700">KPI Id</label>
        <div class="flex items-center space-x-4 mt-1">
          <input type="text" id="kpiId" class="w-1/2 border rounded px-4 py-2" required/>
        </div>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Master Category <span class="text-red-500">*</span></label>
        <input type="text" id="masterCategory" class="mt-1 w-full border rounded px-4 py-2" required readonly/>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Sub Category <span class="text-red-500">*</span></label>
        <input type="text" id="subCategory" class="mt-1 w-full border rounded px-4 py-2" required readonly/>
      </div>

      <div class="md:col-span-2 flex items-center space-x-4">
        <label class="text-sm font-medium text-gray-700">Activity Type:</label>
        <label><input type="radio" name="activity" checked class="mr-1" /> Single Activity</label>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Customer Name <span class="text-red-500">*</span></label>
        <input type="text" value="Amaron" class="mt-1 w-full border rounded px-4 py-2" required/>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Customer Contact No <span class="text-red-500">*</span></label>
        <div class="flex items-center">
          <span class="bg-gray-100 px-3 py-2 border border-r-0 rounded-l text-gray-700">+91</span>
          <input type="text" value="9014137902" class="w-full border rounded-r px-4 py-2" required/>
        </div>
      </div>

      <div class="md:col-span-2">
        <label class="block text-sm font-medium text-gray-700">Customer Address <span class="text-red-500">*</span></label>
        <input type="text" class="mt-1 w-full border rounded px-4 py-2" required/>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Pincode <span class="text-red-500">*</span></label>
        <input type="text" class="mt-1 w-full border rounded px-4 py-2" required/>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Area</label>
        <select class="mt-1 w-full border rounded px-4 py-2">
          <option>All</option>
        </select>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Task Reference Number</label>
        <input type="text" placeholder="Task Reference Number" class="mt-1 w-full border rounded px-4 py-2" required/>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Customer Reference Number</label>
        <input type="text" placeholder="Customer Reference Number" class="mt-1 w-full border rounded px-4 py-2" required/>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Start Date</label>
          <input type="date" value="2024-06-15" class="mt-1 w-full border rounded px-4 py-2" required/>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Number of Days <span class="text-red-500">*</span></label>
        <input type="number" value="5" class="mt-1 w-full border rounded px-4 py-2" required  />
      </div>

    

      <div class="md:col-span-2">
        <button type="submit"
          class="w-full bg-orange-500 text-white py-2 rounded-md hover:bg-orange-600 transition-all">
          Push Task
        </button>
      </div>

    </form>
    </div>
  </div>

  <!-- Ticket Confirmation Popup -->
  <div id="ticketPopup" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center" style="display: none; z-index: 9999;">
    <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4 text-center" style="box-shadow: 0 8px 25px rgba(255, 102, 0, 0.4), 0 4px 15px rgba(255, 102, 0, 0.2);">
      <div class="mb-4">
        <svg class="w-16 h-16 text-green-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <h3 class="text-2xl font-bold text-gray-800 mb-2">Task Confirmed!</h3>
        <p class="text-gray-600 mb-4">Your task has been successfully confirmed.</p>
        <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
          <p class="text-sm text-gray-600 mb-1">Task ID:</p>
          <p id="ticketIdDisplay" class="text-xl font-bold text-orange-600"></p>
        </div>
      </div>
      <button onclick="closeTicketPopup()" class="w-full bg-orange-500 text-white py-3 px-6 rounded-md hover:bg-orange-600 transition-all font-medium">
        OK
      </button>
    </div>
  </div>

  <script>
    // Extract URL parameters and prefill form fields
    function getUrlParams() {
      const params = new URLSearchParams(window.location.search);
      return {
        kpi: params.get('kpi') || '',
        category: params.get('category') || '',
        subcategory: params.get('subcategory') || '',
        row: params.get('row') || ''
      };
    }

    // Populate form fields with URL parameters
    function populateFormFields() {
      const params = getUrlParams();

      // Prefill the form fields
      if (params.kpi) {
        document.getElementById('kpiId').value = params.kpi;
      }
      if (params.category) {
        document.getElementById('masterCategory').value = params.category;
      }
      if (params.subcategory) {
        document.getElementById('subCategory').value = params.subcategory;
      }
    }

    // Initialize form when page loads
    window.addEventListener('load', function() {
      populateFormFields();
    });

    document.querySelector("form").addEventListener("submit", function (e) {
      e.preventDefault();
  
      const customerName = document.querySelector("input[value='Amaron']");
      const contactNo = document.querySelector("input[value='9014137902']");
      const address = document.querySelector("input[type='text']:not([value]):not([readonly]):not([id])");
      const pincode = document.querySelectorAll("input[type='text']:not([value]):not([readonly]):not([id])")[1];
      const days = document.querySelector("input[type='number']");
      let isValid = true;
  
      // Helper to show error
      function showError(input, message) {
        input.classList.add("border-red-500");
        if (!input.nextElementSibling || !input.nextElementSibling.classList.contains("text-red-500")) {
          const error = document.createElement("p");
          error.classList = "text-red-500 text-xs mt-1";
          error.innerText = message;
          input.insertAdjacentElement("afterend", error);
        }
      }
  
      // Helper to clear error
      function clearError(input) {
        input.classList.remove("border-red-500");
        if (input.nextElementSibling && input.nextElementSibling.classList.contains("text-red-500")) {
          input.nextElementSibling.remove();
        }
      }
  
      // Validation Checks
      document.querySelectorAll("input").forEach(input => clearError(input)); // Clear all errors first
  
      if (!customerName.value.trim()) {
        showError(customerName, "Customer name is required");
        isValid = false;
      }
  
      const phoneRegex = /^[6-9]\d{9}$/;
      if (!phoneRegex.test(contactNo.value.trim())) {
        showError(contactNo, "Enter valid 10-digit mobile number starting with 6-9");
        isValid = false;
      }
  
      if (!address.value.trim()) {
        showError(address, "Customer address is required");
        isValid = false;
      }
  
      if (!pincode.value.trim() || !/^\d{6}$/.test(pincode.value.trim())) {
        showError(pincode, "Enter valid 6-digit pincode");
        isValid = false;
      }
  
      if (days.value <= 0) {
        showError(days, "Number of days must be greater than 0");
        isValid = false;
      }
  
      if (isValid) {
        // Generate random ticket ID
        const ticketId = 'TKT-' + Math.random().toString(36).substr(2, 9).toUpperCase();
        showTicketConfirmation(ticketId);
      }
    });

    // Function to show ticket confirmation popup
    function showTicketConfirmation(ticketId) {
      console.log('Showing popup with ticket ID:', ticketId); // Debug log
      const popup = document.getElementById('ticketPopup');
      const ticketIdElement = document.getElementById('ticketIdDisplay');

      if (popup && ticketIdElement) {
        ticketIdElement.textContent = ticketId;
        popup.style.display = 'flex';
        popup.style.zIndex = '9999';
        console.log('Popup should now be visible'); // Debug log
      } else {
        console.error('Popup elements not found'); // Debug log
      }
    }

    // Function to close the popup and clear form data
    function closeTicketPopup() {
      const popup = document.getElementById('ticketPopup');
      popup.style.display = 'none';

      // Clear all form data
      const form = document.querySelector('form');

      // Reset all input fields including readonly ones
      const inputs = form.querySelectorAll('input');
      inputs.forEach(input => {
        if (input.type === 'text' || input.type === 'number' || input.type === 'date') {
          input.value = '';
        } else if (input.type === 'radio') {
          input.checked = false;
        }
      });

      // Reset select dropdowns
      const selects = form.querySelectorAll('select');
      selects.forEach(select => {
        select.selectedIndex = 0;
      });

      // Re-check the single activity radio button (since it should remain selected)
      const singleActivityRadio = form.querySelector('input[type="radio"][name="activity"]');
      if (singleActivityRadio) {
        singleActivityRadio.checked = true;
      }

      console.log('Form data cleared successfully');
    }
  </script>
  
</body>
</html>
