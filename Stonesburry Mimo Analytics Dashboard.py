import streamlit as st
import os
from PIL import Image
import base64
from io import BytesIO
import json
import hashlib
import pandas as pd


# Convert image to base64 for rendering with enhanced quality
def image_to_base64(img, quality=95, optimize=True):
    """
    Convert PIL image to base64 with enhanced quality settings
    """
    buffered = BytesIO()

    # Ensure image is in RGB mode for better quality
    if img.mode in ('RGBA', 'LA', 'P'):
        # Convert to RGB with white background for transparency
        background = Image.new('RGB', img.size, (255, 255, 255))
        if img.mode == 'P':
            img = img.convert('RGBA')
        background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
        img = background
    elif img.mode != 'RGB':
        img = img.convert('RGB')

    # Save with high quality settings
    img.save(buffered, format="PNG", optimize=optimize, quality=quality)
    return base64.b64encode(buffered.getvalue()).decode()

# Helper function to reset category results display
def reset_category_results():
    """Reset the category results display and update tracking variables"""
    st.session_state.show_category_results = False

# Enhanced zoom controls component
def render_zoom_controls(image_type="main"):
    st.markdown("#### 🔍 Image Zoom & Navigation")
    col1, col2, col3, col4 = st.columns([1, 1, 1, 2])
    
    with col1:
        if st.button("🔍➖ Zoom Out", key=f"zoom_out_{image_type}"):
            if st.session_state.zoom_level > 25:
                st.session_state.zoom_level = max(25, st.session_state.zoom_level - 25)
                st.rerun()
    
    with col2:
        if st.button("🔄 Reset", key=f"zoom_reset_{image_type}"):
            st.session_state.zoom_level = 100
            st.rerun()
    
    with col3:
        if st.button("🔍➕ Zoom In", key=f"zoom_in_{image_type}"):
            if st.session_state.zoom_level < 300:
                st.session_state.zoom_level = min(300, st.session_state.zoom_level + 25)
                st.rerun()
    
    with col4:
        # Zoom slider with current value
        selected_zoom = st.slider(
            "🎚️ Zoom Level",
            min_value=25,
            max_value=300,
            value=st.session_state.zoom_level,
            step=5,
            key=f"zoom_slider_{image_type}",
            help="Drag to adjust zoom level (25% - 300%)",
            label_visibility="collapsed"
        )
        if selected_zoom != st.session_state.zoom_level:
            st.session_state.zoom_level = selected_zoom
            st.rerun()

# Helper function to get the correct image folder based on state selection
def get_image_folder_by_state(selected_state):
    """
    Returns the appropriate image folder path based on the selected state.

    Args:
        selected_state (str): The selected state ("Delhi(NCT)" or "West Bengal")

    Returns:
        str: The folder path for the corresponding state's images
    """
    if selected_state == "Delhi(NCT)":
        return os.path.join("GeoIQimages", "YusufSaraiGEOIQImages")
    elif selected_state == "West Bengal":
        return os.path.join("GeoIQimages", "South24ParganasGEOIQImages")
    else:
        # Default to Delhi images if state is not recognized
        return os.path.join("GeoIQimages", "YusufSaraiGEOIQImages")

# Helper function to get the correct item image folder based on state selection
def get_item_image_folder_by_state(selected_state):
    """
    Returns the appropriate item image folder path based on the selected state.

    Args:
        selected_state (str): The selected state ("Delhi(NCT)" or "West Bengal")

    Returns:
        str: The folder path for the corresponding state's item images
    """
    if selected_state == "Delhi(NCT)":
        return os.path.join("Items", "YusufSaraiItemPrice")
    elif selected_state == "West Bengal":
        return os.path.join("Items", "South24ParganasItemPrices")
    else:
        # Default to Delhi item images if state is not recognized
        return os.path.join("Items", "YusufSaraiItemPrice")

# Enhanced zoomable image component
def render_zoomable_image(encoded_image, title, zoom_level, image_id=None):
    if image_id is None:
        image_id = hashlib.md5(title.encode()).hexdigest()[:8]
    
    # Determine if dragging should be enabled
    drag_enabled = "true" if zoom_level > 100 else "false"
    cursor_style = "grab" if zoom_level > 100 else "default"
    
    zoom_html = f"""
    <style>
        .zoom-container-{image_id} {{
            width: 100%;
            height: 600px;
            overflow: auto;
            border: 2px solid #ff6600;
            background-color: #f8f9fa;
            text-align: center;
            position: relative;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }}
        
        .zoom-image-{image_id} {{
            width: {zoom_level}%;
            height: auto;
            transition: width 0.3s ease, transform 0.3s ease;
            transform-origin: center center;
            cursor: {cursor_style};
            display: block;
            margin: 0 auto;
            /* Enhanced image quality settings */
            image-rendering: -webkit-optimize-contrast;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            image-rendering: pixelated;
            image-rendering: high-quality;
            /* Smooth scaling for better quality */
            -ms-interpolation-mode: bicubic;
            /* Additional quality enhancements */
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
            /* Anti-aliasing */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }}
        
        .zoom-info-{image_id} {{
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.95);
            padding: 8px 12px;
            border-radius: 5px;
            color: #ff6600;
            font-weight: bold;
            border: 1px solid #ff6600;
            font-size: 14px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }}
        
        .zoom-instructions-{image_id} {{
            position: absolute;
            bottom: 10px;
            left: 10px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.95);
            padding: 6px 10px;
            border-radius: 5px;
            color: #333;
            font-size: 12px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }}
    </style>
    
    <div class="zoom-container-{image_id}">
        <div class="zoom-info-{image_id}">
            📏 {zoom_level}%
        </div>
        {f'<div class="zoom-instructions-{image_id}">🖱️ Click and drag to pan</div>' if zoom_level > 100 else ''}
        <img class="zoom-image-{image_id}" src="data:image/png;base64,{encoded_image}" alt="{title}">
    </div>
    
    <script>
        (function() {{
            // Zoom functionality for image {image_id}
            let isDragging_{image_id} = false;
            let startX_{image_id}, startY_{image_id}, scrollLeft_{image_id}, scrollTop_{image_id};
            
            const container_{image_id} = document.querySelector('.zoom-container-{image_id}');
            const img_{image_id} = document.querySelector('.zoom-image-{image_id}');
            
            if (!container_{image_id} || !img_{image_id}) return;
            
            // Only enable dragging if zoom > 100%
            if ({drag_enabled}) {{
                // Mouse down event
                img_{image_id}.addEventListener('mousedown', (e) => {{
                    isDragging_{image_id} = true;
                    startX_{image_id} = e.pageX - container_{image_id}.offsetLeft;
                    startY_{image_id} = e.pageY - container_{image_id}.offsetTop;
                    scrollLeft_{image_id} = container_{image_id}.scrollLeft;
                    scrollTop_{image_id} = container_{image_id}.scrollTop;
                    img_{image_id}.style.cursor = 'grabbing';
                    e.preventDefault();
                }});
                
                // Mouse move event
                container_{image_id}.addEventListener('mousemove', (e) => {{
                    if (!isDragging_{image_id}) return;
                    e.preventDefault();
                    const x = e.pageX - container_{image_id}.offsetLeft;
                    const y = e.pageY - container_{image_id}.offsetTop;
                    const walkX = (x - startX_{image_id}) * 1.5;
                    const walkY = (y - startY_{image_id}) * 1.5;
                    container_{image_id}.scrollLeft = scrollLeft_{image_id} - walkX;
                    container_{image_id}.scrollTop = scrollTop_{image_id} - walkY;
                }});
                
                // Mouse up/leave events
                const stopDragging = () => {{
                    isDragging_{image_id} = false;
                    img_{image_id}.style.cursor = 'grab';
                }};
                
                container_{image_id}.addEventListener('mouseup', stopDragging);
                container_{image_id}.addEventListener('mouseleave', stopDragging);
                
                // Prevent image drag default behavior
                img_{image_id}.addEventListener('dragstart', (e) => e.preventDefault());
            }}
            
            // Mouse wheel zoom (optional enhancement)
            container_{image_id}.addEventListener('wheel', (e) => {{
                if (e.ctrlKey) {{
                    e.preventDefault();
                    // This would require additional Streamlit integration for wheel zoom
                }}
            }});
        }})();
    </script>
    """
    
    return zoom_html

# App config
st.set_page_config(
    layout="wide",
    page_title="Stonesburry Mimo Analytics Dashboard",
    initial_sidebar_state="expanded"
)

# Add logo at the top-left corner using Streamlit container
def add_logo():
    """Add logo to the top-left corner of the page"""
    try:
        # Load and encode the logo
        from PIL import Image
        import base64
        from io import BytesIO

        # Load the new logos
        stonesbury_path = "logo/Stonesbury-logo.png"
        adobe_path = "logo/Adobe Express - file.png"

        logos_html = ""
        if os.path.exists(stonesbury_path):
            stonesbury_img = Image.open(stonesbury_path)
            buffered = BytesIO()
            stonesbury_img.save(buffered, format="PNG")
            stonesbury_base64 = base64.b64encode(buffered.getvalue()).decode()
            logos_html += f'<img src="data:image/png;base64,{stonesbury_base64}" alt="Stonesbury Logo" style="height: 50px; width: auto; margin-right: 15px;">'

        if os.path.exists(adobe_path):
            adobe_img = Image.open(adobe_path)
            buffered = BytesIO()
            adobe_img.save(buffered, format="PNG")
            adobe_base64 = base64.b64encode(buffered.getvalue()).decode()
            logos_html += f'<img src="data:image/png;base64,{adobe_base64}" alt="Adobe Express Logo" style="height: 65px; width: auto;">'  # Increased size for Adobe Express logo on main page

        if logos_html:
            # Use Streamlit's container approach with absolute positioning
            st.markdown(f"""
            <div style="position: absolute; top: 0; left: 0; z-index: 999; background: rgba(255,255,255,0.9); padding: 8px; border-radius: 8px; margin: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.15);">
                {logos_html}
            </div>
            """, unsafe_allow_html=True)
        else:
            # Fallback if logo file not found
            st.markdown("""
            <div style="position: absolute; top: 0; left: 0; z-index: 999; background: rgba(255,255,255,0.9); padding: 8px; border-radius: 8px; margin: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.15);">
                <div style="height: 45px; width: 90px; background: #ff6600; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold; border-radius: 5px; font-size: 13px;">
                    GeoIQ
                </div>
            </div>
            """, unsafe_allow_html=True)
    except Exception:
        # Silent fallback - don't disrupt the app if logo fails
        pass

# Add logo using Streamlit's native approach
def add_logo_native():
    """Add logo using Streamlit's native image display"""
    try:
        # Load the new logos
        stonesbury_path = "logo/Stonesbury-logo.png"
        adobe_path = "logo/Adobe Express - file.png"

        if os.path.exists(stonesbury_path) or os.path.exists(adobe_path):
            # Create columns for layout - left logo, center warning, right logo
            col1, col2, col3 = st.columns([1, 6, 1])

            with col1:
                # Display Stonesbury logo on the left
                if os.path.exists(stonesbury_path):
                    st.markdown('<div style="margin-top: -10px;">', unsafe_allow_html=True)
                    st.image(stonesbury_path, width=120)
                    st.markdown('</div>', unsafe_allow_html=True)

            with col2:
                # Check for fresh login warning message first
                if st.session_state.fresh_login and not st.session_state.warning_shown:
                    # Use Streamlit's built-in color functionality for better reliability
                    st.markdown("""
                    <div style="text-align: center; margin-top: 10px;">
                    </div>
                    """, unsafe_allow_html=True)
                    st.markdown(
                        '<p id="warning-message-main" class="red-warning" style="text-align: center; font-size: 28px; font-weight: bold; margin: 0; color: #FF0000 !important;">Not all libraries are connected</p>',
                        unsafe_allow_html=True
                    )
                    # Add JavaScript to force red color
                    st.markdown("""
                    <script>
                    function forceRedWarning() {
                        const warningElement = document.getElementById('warning-message-main');
                        if (warningElement) {
                            warningElement.style.setProperty('color', 'red', 'important');
                            warningElement.style.setProperty('background-color', 'transparent', 'important');
                            console.log('Warning message forced to red');
                        }

                        // Also target by class
                        const redWarnings = document.querySelectorAll('.red-warning');
                        redWarnings.forEach(el => {
                            el.style.setProperty('color', 'red', 'important');
                            el.style.setProperty('background-color', 'transparent', 'important');
                        });

                        // Target by text content
                        const allElements = document.querySelectorAll('*');
                        allElements.forEach(el => {
                            if (el.textContent && el.textContent.includes('Not all libraries are connected')) {
                                el.style.setProperty('color', 'red', 'important');
                                el.style.setProperty('background-color', 'transparent', 'important');
                            }
                        });
                    }

                    // Run immediately
                    setTimeout(forceRedWarning, 100);

                    // Keep trying for a few seconds
                    let attempts = 0;
                    const interval = setInterval(() => {
                        forceRedWarning();
                        attempts++;
                        if (attempts >= 20) {
                            clearInterval(interval);
                        }
                    }, 200);
                    </script>
                    """, unsafe_allow_html=True)
                    # Mark warning as shown and reset fresh login flag
                    st.session_state.warning_shown = True
                    st.session_state.fresh_login = False
                else:
                    # Empty space when no fresh login warning
                    pass

            with col3:
                # Display Adobe Express logo on the right
                if os.path.exists(adobe_path):
                    st.markdown('<div style="margin-top: -10px;">', unsafe_allow_html=True)
                    st.image(adobe_path, width=300)  # Increased size for Adobe Express logo on main page
                    st.markdown('</div>', unsafe_allow_html=True)

            # Add some spacing
            st.markdown("<br>", unsafe_allow_html=True)
        else:
            # Fallback text logo
            col1, col2, col3 = st.columns([1, 6, 1])
            with col1:
                st.markdown("""
                <div style="background: #ff6600; color: white; padding: 15px; border-radius: 8px; text-align: center; font-weight: bold; width: 180px; font-size: 18px;">
                    GeoIQ
                </div>
                """, unsafe_allow_html=True)

            with col2:
                # Check for fresh login warning message first
                if st.session_state.fresh_login and not st.session_state.warning_shown:
                    # Use Streamlit's built-in color functionality for better reliability
                    st.markdown("""
                    <div style="text-align: center; margin-top: 10px;">
                    </div>
                    """, unsafe_allow_html=True)
                    st.markdown(
                        '<p id="warning-message-fallback" class="red-warning" style="text-align: center; font-size: 28px; font-weight: bold; margin: 0; color: #FF0000 !important;">Not all libraries are connected</p>',
                        unsafe_allow_html=True
                    )
                    # Add JavaScript to force red color (fallback)
                    st.markdown("""
                    <script>
                    function forceRedWarningFallback() {
                        const warningElement = document.getElementById('warning-message-fallback');
                        if (warningElement) {
                            warningElement.style.setProperty('color', 'red', 'important');
                            warningElement.style.setProperty('background-color', 'transparent', 'important');
                            console.log('Fallback warning message forced to red');
                        }

                        // Also target by class
                        const redWarnings = document.querySelectorAll('.red-warning');
                        redWarnings.forEach(el => {
                            el.style.setProperty('color', 'red', 'important');
                            el.style.setProperty('background-color', 'transparent', 'important');
                        });

                        // Target by text content
                        const allElements = document.querySelectorAll('*');
                        allElements.forEach(el => {
                            if (el.textContent && el.textContent.includes('Not all libraries are connected')) {
                                el.style.setProperty('color', 'red', 'important');
                                el.style.setProperty('background-color', 'transparent', 'important');
                            }
                        });
                    }

                    // Run immediately
                    setTimeout(forceRedWarningFallback, 100);

                    // Keep trying for a few seconds
                    let attempts = 0;
                    const interval = setInterval(() => {
                        forceRedWarningFallback();
                        attempts++;
                        if (attempts >= 20) {
                            clearInterval(interval);
                        }
                    }, 200);
                    </script>
                    """, unsafe_allow_html=True)
                    # Mark warning as shown and reset fresh login flag
                    st.session_state.warning_shown = True
                    st.session_state.fresh_login = False
                else:
                    # Empty space when no fresh login warning (fallback)
                    pass

            with col3:
                # Fallback logo space (empty for now)
                st.markdown("")

            # Add some spacing
            st.markdown("<br>", unsafe_allow_html=True)
    except Exception:
        # Silent fallback - don't disrupt the app if logo fails
        pass

# Logo will be added after sidebar processing to get current dropdown values

# Add timestamp display function
def add_timestamp_display():
    """Add live timestamp display above the dashboard content"""
    import datetime
    current_time = datetime.datetime.now()
    formatted_time = current_time.strftime("%B %d, %Y - %I:%M:%S %p")

    st.markdown(f"""
    <div style="
        color: #ff6600;
        text-align: right;
        font-weight: bold;
        font-size: 18px;
        margin: 10px 0;
    ">
        <div style="font-size: 20px;">{formatted_time}</div>
    </div>
    """, unsafe_allow_html=True)

# Add JavaScript for real-time timestamp updates
st.markdown("""
<script>
function updateTimestamp() {
    const now = new Date();
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
    };
    const formattedTime = now.toLocaleDateString('en-US', options).replace(',', ' -');

    // Find all timestamp elements and update them
    const timestampElements = document.querySelectorAll('div[style*="font-size: 15px"], div[style*="font-size: 20px"]');
    timestampElements.forEach(element => {
        if (element.parentElement && element.parentElement.style.color.includes('#ff6600')) {
            element.textContent = formattedTime;
        }
    });
}

// Update timestamp every second
setInterval(updateTimestamp, 1000);

// Initial update
updateTimestamp();
</script>
""", unsafe_allow_html=True)

# Add JavaScript for real-time validation monitoring
st.markdown("""
<script>
// Real-time validation monitoring
function setupRealtimeValidation() {
    // Valid combinations
    const validCombinations = [
        ["Delhi", "South Central", "Yusuf Sarai"],
        ["West Bengal", "East", "South 24 Parganas"]
    ];

    function validateCombination() {
        // Get current dropdown values using multiple selector strategies
        let state = null, geography = null, locality = null;

        // Strategy 1: Try to find dropdowns by their position in sidebar
        const sidebarSelects = document.querySelectorAll('[data-testid="stSidebar"] select');
        if (sidebarSelects.length >= 3) {
            state = sidebarSelects[0].value;
            geography = sidebarSelects[1].value;
            locality = sidebarSelects[2].value;
        } else {
            // Strategy 2: Try all selects on page
            const allSelects = document.querySelectorAll('select');
            if (allSelects.length >= 3) {
                state = allSelects[0].value;
                geography = allSelects[1].value;
                locality = allSelects[2].value;
            }
        }

        if (state && geography && locality) {
            const currentCombination = [state, geography, locality];
            const isValid = validCombinations.some(valid =>
                valid[0] === currentCombination[0] &&
                valid[1] === currentCombination[1] &&
                valid[2] === currentCombination[2]
            );

            updateErrorDisplay(!isValid);
        }
    }

    function updateErrorDisplay(showError) {
        // Find main content error message element
        const mainErrorElement = document.getElementById('main-error-text');

        if (mainErrorElement) {
            if (showError) {
                mainErrorElement.style.display = 'block';
                mainErrorElement.style.setProperty('color', '#FF0000', 'important');
                mainErrorElement.style.setProperty('font-size', '32px', 'important');
                mainErrorElement.style.setProperty('font-weight', 'bold', 'important');
                mainErrorElement.style.setProperty('background-color', 'transparent', 'important');
                mainErrorElement.style.setProperty('border', 'none', 'important');
                mainErrorElement.style.setProperty('padding', '0', 'important');
                mainErrorElement.style.setProperty('margin', '0', 'important');
                mainErrorElement.textContent = 'Libraries are not connected';
            } else {
                mainErrorElement.style.display = 'none';
            }
        }

        // Also force red color on any "Libraries are not connected" text
        if (showError) {
            const allElements = document.querySelectorAll('*');
            allElements.forEach(el => {
                if (el.textContent && el.textContent.includes('Libraries are not connected')) {
                    el.style.setProperty('color', '#FF0000', 'important');
                    el.style.setProperty('background-color', 'transparent', 'important');
                }
            });
        }
    }

    // Monitor dropdown changes
    function attachDropdownListeners() {
        const allSelects = document.querySelectorAll('select');
        allSelects.forEach(select => {
            select.addEventListener('change', validateCombination);
        });

        // Also monitor for new dropdowns being added
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) {
                            const newSelects = node.querySelectorAll('select');
                            newSelects.forEach(select => {
                                select.addEventListener('change', validateCombination);
                            });
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Initial validation
    setTimeout(validateCombination, 1000);

    // Attach listeners
    setTimeout(attachDropdownListeners, 1000);

    // Keep checking periodically for changes
    setInterval(validateCombination, 2000);
}

// Initialize real-time validation
document.addEventListener('DOMContentLoaded', setupRealtimeValidation);
setTimeout(setupRealtimeValidation, 1000);
</script>
""", unsafe_allow_html=True)



# --- Custom CSS ---
st.markdown("""
    <style>
        html, body, [data-testid="stApp"] {
            background-color: white !important;
            color: #333 !important;
        }
        
        /* Sidebar styling */
        .stSidebar {
            background-color: #f8f9fa !important;
        }
        .stSidebar > div {
            background-color: #f8f9fa !important;
        }
        
        /* All text in sidebar should be visible */
        .stSidebar * {
            color: #333 !important;
        }
        
        /* Headers and labels */
        h1, h2, h3, h4, h5, h6, label, .stSelectbox label, .stRadio label, .stMarkdown h2, .stMarkdown h3, .stMarkdown h4 {
            color: #ff6600 !important;
        }
        
        /* Sidebar headers specifically */
        .stSidebar h1, .stSidebar h2, .stSidebar h3, .stSidebar h4, .stSidebar h5, .stSidebar h6,
        .stSidebar .stMarkdown h2, .stSidebar .stMarkdown h3, .stSidebar .stMarkdown h4 {
            color: #ff6600 !important;
        }
        
        /* Button styling */
        .stButton > button {
            background-color: #f8f9fa !important;
            color: #333 !important;
            border: 1px solid #ff6600 !important;
            padding: 8px 10px !important;
            font-size: 14px !important;
            width: 100% !important;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: all 0.3s ease;
        }
        .stButton > button:hover {
            background-color: #fff3e0 !important;
            border-color: #ff6600 !important;
            box-shadow: 0 2px 4px rgba(255, 102, 0, 0.2) !important;
        }
        
        /* Selectbox styling */
        .stSelectbox > div > div {
            background-color: white !important;
            border: 1px solid #ddd !important;
            color: #333 !important;
        }
        .stSidebar .stSelectbox > div > div {
            background-color: white !important;
            color: #333 !important;
        }
        .stSelectbox div[data-baseweb="select"] {
            background-color: white !important;
        }
        
        /* Dropdown menu options */
        .stSelectbox ul {
            background-color: white !important;
            color: #333 !important;
        }
        .stSelectbox li {
            background-color: white !important;
            color: #333 !important;
        }
        .stSelectbox li:hover {
            background-color: #f0f0f0 !important;
            color: #333 !important;
        }
        
        /* Base UI dropdown styling */
        [data-baseweb="menu"] {
            background-color: white !important;
        }
        [data-baseweb="menu"] ul {
            background-color: white !important;
        }
        [data-baseweb="menu"] li {
            background-color: white !important;
            color: #333 !important;
        }
        [data-baseweb="menu"] li:hover {
            background-color: #f0f0f0 !important;
            color: #333 !important;
        }
        
        /* More specific dropdown option styling */
        div[role="listbox"] {
            background-color: white !important;
        }
        div[role="option"] {
            background-color: white !important;
            color: #333 !important;
        }
        div[role="option"]:hover {
            background-color: #f0f0f0 !important;
            color: #333 !important;
        }
        
        /* Dropdown container */
        .css-1n76uvr {
            background-color: white !important;
        }
        .css-1aumxhk {
            background-color: white !important;
        }
        
        /* Select dropdown popup */
        [data-baseweb="popover"] {
            background-color: white !important;
        }
        [data-baseweb="popover"] > div {
            background-color: white !important;
        }
        
                 /* Any remaining dropdown elements */
         .Select__menu {
             background-color: white !important;
         }
         .Select__option {
             background-color: white !important;
             color: #333 !important;
         }
         .Select__option:hover {
             background-color: #f0f0f0 !important;
             color: #333 !important;
         }
         
         /* More aggressive dropdown targeting */
         div[data-testid="stSelectbox"] div[data-baseweb="select"] > div {
             background-color: white !important;
             color: #333 !important;
         }
         
         /* Target the actual dropdown list */
         div[data-testid="stSelectbox"] ul {
             background-color: white !important;
         }
         div[data-testid="stSelectbox"] li {
             background-color: white !important;
             color: #333 !important;
         }
         div[data-testid="stSelectbox"] li > div {
             background-color: white !important;
             color: #333 !important;
         }
         
         /* BaseWeb components */
         .css-1n76uvr-StyledDropdownListItem {
             background-color: white !important;
             color: #333 !important;
         }
         .css-1wa3eu0-StyledDropdownListItem {
             background-color: white !important;
             color: #333 !important;
         }
         
         /* All possible CSS class variations */
         [class*="css-"][class*="StyledDropdownListItem"] {
             background-color: white !important;
             color: #333 !important;
         }
         [class*="css-"][class*="StyledList"] {
             background-color: white !important;
         }
         [class*="css-"][class*="dropdown"] {
             background-color: white !important;
             color: #333 !important;
         }
         
         /* Streamlit specific classes */
         .row-widget.stSelectbox > div > div > div {
             background-color: white !important;
             color: #333 !important;
         }
         
         /* Universal dropdown override */
         div[role="combobox"] ~ div {
             background-color: white !important;
         }
         div[role="combobox"] ~ div * {
             background-color: white !important;
             color: #333 !important;
         }
        
                 /* Radio button styling - minimal to keep them visible */
         .stRadio > div {
             background-color: transparent !important;
         }
         .stRadio label {
             color: #333 !important;
         }
         .stSidebar .stRadio label {
             color: #333 !important;
         }
        
        /* Text and paragraph styling */
        p, span, div {
            color: #333 !important;
        }
        .stSidebar p, .stSidebar span, .stSidebar div {
            color: #333 !important;
        }
        
        /* Zoom control section */
        .zoom-control-section {
            background: linear-gradient(145deg, #f8f9fa, #ffffff);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #ff6600;
            margin: 10px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        /* Main content area */
        .main .block-container {
            background-color: white !important;
        }
        
        /* Slider styling */
        .stSlider > div > div {
            color: #333 !important;
        }

        /* Hide any stray input elements that might appear */
        .stTextInput:not(.stSidebar .stTextInput) {
            display: none !important;
        }

        /* Hide any empty input containers */
        div[data-testid="stTextInput"]:empty {
            display: none !important;
        }

        /* Hide any orphaned input elements in main content */
        .main input:not(.zoom-control-section input):not([data-testid="stSidebar"] input) {
            display: none !important;
        }

        /* Hide any empty form elements */
        .stForm:empty, .stContainer:empty {
            display: none !important;
        }

        /* Aggressively hide any input elements that shouldn't be there */
        .main .stTextInput, .main .stNumberInput, .main .stTextArea {
            display: none !important;
        }

        /* Exception: Allow inputs only in zoom control sections */
        .zoom-control-section .stTextInput,
        .zoom-control-section .stNumberInput,
        .zoom-control-section .stSlider {
            display: block !important;
        }
        
        /* Fix for any remaining invisible text */
        [data-testid="stSidebar"] * {
            color: #333 !important;
        }
        [data-testid="stSidebar"] h1, [data-testid="stSidebar"] h2, [data-testid="stSidebar"] h3, [data-testid="stSidebar"] h4 {
            color: #ff6600 !important;
        }
        
        /* Force light theme on all Streamlit elements */
        .stApp {
            background-color: white !important;
            color: #333 !important;
        }
        
        /* Override any dark backgrounds */
        .stApp > header {
            background-color: white !important;
        }
        
        /* Main container */
        .main > div {
            background-color: white !important;
        }
        
        /* Force specific elements to be dark, but not all elements */
        .stSidebar, .stSidebar *, .main, .main div, .main span:not(.red-warning) {
            color: #333 !important;
        }

        /* Exception for headers - keep them orange */
        h1, h2, h3, h4, h5, h6 {
            color: #ff6600 !important;
        }

        /* Exception for specific header classes */
        .stMarkdown h1, .stMarkdown h2, .stMarkdown h3, .stMarkdown h4, .stMarkdown h5, .stMarkdown h6 {
            color: #ff6600 !important;
        }

        /* Exception for error messages - force red color */
        .error-message-red, .error-message-red * {
            color: #FF0000 !important;
        }

        /* Login warning message - force red color with highest specificity */
        div.login-warning-message,
        div.login-warning-message *,
        .login-warning-message,
        .login-warning-message *,
        .login-warning-message span {
            color: #FF0000 !important;
            background: transparent !important;
        }

        /* Force red color for "Not all libraries are connected" text */
        span[style*="color: #FF0000"] {
            color: #FF0000 !important;
        }

        /* More specific targeting for the warning text */
        div span[style*="#FF0000"] {
            color: #FF0000 !important;
        }

        /* Override any global text color for red warning spans */
        span[style*="color: #FF0000 !important"] {
            color: #FF0000 !important;
        }

        /* Red warning class - highest priority */
        .red-warning {
            color: #FF0000 !important;
        }

        /* Force red color with maximum specificity */
        p.red-warning {
            color: #FF0000 !important;
        }

        /* Ultimate override for red warning text */
        .red-warning, .red-warning *, p.red-warning, div p.red-warning {
            color: #FF0000 !important;
        }

        /* Force red color on any element containing the warning text */
        p[style*="color: #FF0000"] {
            color: #FF0000 !important;
        }
        
        /* Force sidebar background */
        section[data-testid="stSidebar"] {
            background-color: #f8f9fa !important;
        }
        section[data-testid="stSidebar"] > div {
            background-color: #f8f9fa !important;
        }
        
        /* Force main content background */
        .block-container {
            background-color: white !important;
        }
        
        /* Override any remaining dark elements */
        div[data-testid="stVerticalBlock"] {
            background-color: transparent !important;
        }
        
        /* Input fields */
        .stTextInput > div > div > input {
            background-color: white !important;
            color: #333 !important;
            border: 1px solid #ddd !important;
        }
        
        /* Number input */
        .stNumberInput > div > div > input {
            background-color: white !important;
            color: #333 !important;
            border: 1px solid #ddd !important;
        }
        
        /* Text area */
        .stTextArea > div > div > textarea {
            background-color: white !important;
            color: #333 !important;
            border: 1px solid #ddd !important;
        }
        
        /* Date input */
        .stDateInput > div > div > input {
            background-color: white !important;
            color: #333 !important;
            border: 1px solid #ddd !important;
        }
        
        /* Checkbox */
        .stCheckbox {
            color: #333 !important;
        }
        
        /* Metric values */
        .metric-container {
            background-color: white !important;
        }
        
        /* Tables */
        .dataframe {
            background-color: white !important;
            color: #333 !important;
        }
        
        /* Code blocks */
        .stCodeBlock {
            background-color: #f8f9fa !important;
            color: #333 !important;
        }
        
        /* Progress bars */
        .stProgress {
            background-color: #f8f9fa !important;
        }
        
        /* Alerts */
        .stAlert {
            background-color: white !important;
            color: #333 !important;
        }
        
        /* File uploader */
        .stFileUploader {
            background-color: white !important;
            color: #333 !important;
        }
        
        /* Expander */
        .streamlit-expanderHeader {
            background-color: #f8f9fa !important;
            color: #333 !important;
        }
        
        /* Tabs */
        .stTabs {
            background-color: white !important;
        }
        
                 /* Form */
         .stForm {
             background-color: white !important;
             border: 1px solid #ddd !important;
         }
         
         /* Catch-all for any remaining dark elements */
         *[style*="background-color: black"] {
             background-color: white !important;
             color: #333 !important;
         }
         *[style*="background: black"] {
             background-color: white !important;
             color: #333 !important;
         }
         *[style*="background-color: rgb(0, 0, 0)"] {
             background-color: white !important;
             color: #333 !important;
         }
         
         /* Override any potential dark theme CSS variables */
         :root {
             --background-color: white !important;
             --text-color: #333 !important;
             --secondary-background-color: #f8f9fa !important;
         }
         
         /* ULTIMATE OVERRIDE - Force everything to be light */
         *:not(.stRadio):not(.stRadio *):not(input[type="radio"]):not([role="radio"]):not([role="radiogroup"]):not(.error-message-red):not(.error-message-red *) {
             background-color: white !important;
             color: #333 !important;
         }
         
         /* Exception for the main app background */
         .stApp {
             background-color: white !important;
         }
         
         /* Exception for the sidebar */
         .stSidebar, .stSidebar > div {
             background-color: #f8f9fa !important;
         }
         
         /* Keep headers orange */
         h1, h2, h3, h4, h5, h6, 
         .stMarkdown h1, .stMarkdown h2, .stMarkdown h3, .stMarkdown h4, .stMarkdown h5, .stMarkdown h6 {
             color: #ff6600 !important;
             background-color: transparent !important;
         }
         
         /* Button colors */
         .stButton > button {
             background-color: #f8f9fa !important;
             color: #333 !important;
             border: 1px solid #ff6600 !important;
         }
         .stButton > button:hover {
             background-color: #fff3e0 !important;
         }
         
         /* Specific image container background */
         .zoom-container, [class*="zoom-container"] {
             background-color: #f8f9fa !important;
         }

         /* FINAL OVERRIDE - Force error messages to be red */
         div.error-message-red {
             color: #FF0000 !important;
         }
         div.error-message-red * {
             color: #FF0000 !important;
         }
         [class*="error-message-red"] {
             color: #FF0000 !important;
         }
         [class*="error-message-red"] * {
             color: #FF0000 !important;
         }

         /* ID-based overrides for maximum specificity */
         #error-msg-1, #error-msg-2 {
             color: #FF0000 !important;
         }
         #error-msg-1 *, #error-msg-2 * {
             color: #FF0000 !important;
         }
         #error-msg-1 span, #error-msg-2 span {
             color: #FF0000 !important;
         }

         /* Main box error message overrides */
         #error-box-main {
             color: #FF0000 !important;
         }
         #error-box-main * {
             color: #FF0000 !important;
         }
         #error-box-main h2 {
             color: #FF0000 !important;
         }
         #error-box-main span {
             color: #FF0000 !important;
         }
         #error-box-text {
             color: #FF0000 !important;
         }
         #error-box-text * {
             color: #FF0000 !important;
         }

         /* ABSOLUTE FINAL OVERRIDE - Red warning text (MUST BE LAST) */
         .red-warning {
             color: red !important;
         }
         p.red-warning {
             color: red !important;
         }
         .red-warning * {
             color: red !important;
         }
         /* Force red with maximum CSS specificity */
         html body .red-warning {
             color: red !important;
         }
         html body p.red-warning {
             color: red !important;
         }

    </style>
     
     <script>
         // Force light theme by removing any dark theme classes
         function forceLightTheme() {
             // Remove dark theme classes from body and html
             document.body.classList.remove('dark-theme', 'dark');
             document.documentElement.classList.remove('dark-theme', 'dark');
             
             // Set light theme attributes
             document.body.setAttribute('data-theme', 'light');
             document.documentElement.setAttribute('data-theme', 'light');
             
                           // Override any inline dark styles
              const allElements = document.querySelectorAll('*');
              allElements.forEach(el => {
                  const computed = window.getComputedStyle(el);
                  if (computed.backgroundColor === 'rgb(0, 0, 0)' || computed.backgroundColor === 'black') {
                      el.style.backgroundColor = 'white';
                  }
                  if (computed.color === 'rgb(255, 255, 255)' || computed.color === 'white') {
                      if (!el.closest('h1, h2, h3, h4, h5, h6')) {
                          el.style.color = '#333';
                      }
                  }
              });
              
              // Specifically target dropdown elements
              const dropdownElements = document.querySelectorAll(
                  '[data-baseweb="menu"], [data-baseweb="popover"], [role="listbox"], [role="option"], ' +
                  '.stSelectbox ul, .stSelectbox li, .Select__menu, .Select__option'
              );
              dropdownElements.forEach(el => {
                  el.style.backgroundColor = 'white';
                  el.style.color = '#333';
              });
              
              // Target any element with black background
              const blackElements = document.querySelectorAll('[style*="background-color: black"], [style*="background: black"]');
              blackElements.forEach(el => {
                  el.style.backgroundColor = 'white';
                  el.style.color = '#333';
              });
              

         }
         
                   // Run on page load
          document.addEventListener('DOMContentLoaded', forceLightTheme);
          
          // Run repeatedly to catch dynamically added content
          setInterval(forceLightTheme, 500);
          
          // Run immediately
          forceLightTheme();
          
          // Create a mutation observer to catch dropdown changes
          const observer = new MutationObserver(function(mutations) {
              mutations.forEach(function(mutation) {
                  if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                      // Check if any dropdown elements were added
                      mutation.addedNodes.forEach(function(node) {
                          if (node.nodeType === 1) { // Element node
                              // Force style on the new element and its children
                              const newElement = node;
                              const allNew = [newElement, ...newElement.querySelectorAll('*')];
                              
                              allNew.forEach(el => {
                                  // Force white background on any dark elements
                                  const computed = window.getComputedStyle(el);
                                  if (computed.backgroundColor === 'rgb(0, 0, 0)' || 
                                      computed.backgroundColor === 'black' ||
                                      computed.backgroundColor === 'rgba(0, 0, 0, 1)') {
                                      el.style.setProperty('background-color', 'white', 'important');
                                      el.style.setProperty('color', '#333', 'important');
                                  }
                                  if (computed.color === 'rgb(255, 255, 255)' || 
                                      computed.color === 'white' ||
                                      computed.color === 'rgba(255, 255, 255, 1)') {
                                      if (!el.closest('h1, h2, h3, h4, h5, h6')) {
                                          el.style.setProperty('color', '#333', 'important');
                                      }
                                  }
                                  
                                  // Specifically target dropdown-related elements
                                  if (el.getAttribute('role') === 'option' ||
                                      el.getAttribute('role') === 'listbox' ||
                                      el.getAttribute('data-baseweb') === 'menu' ||
                                      el.getAttribute('data-baseweb') === 'popover' ||
                                      el.classList.toString().includes('dropdown') ||
                                      el.classList.toString().includes('Select') ||
                                      el.classList.toString().includes('css-')) {
                                      el.style.setProperty('background-color', 'white', 'important');
                                      el.style.setProperty('color', '#333', 'important');
                                  }

                              });
                          }
                      });
                      
                      // Run the main theme function again
                      forceLightTheme();
                  }
              });
          });
          
          // Start observing
          observer.observe(document.body, {
              childList: true,
              subtree: true
          });
     </script>
""", unsafe_allow_html=True)

# --- Session state ---
if "selected_button" not in st.session_state:
    st.session_state.selected_button = None
if "zoom_level" not in st.session_state:
    st.session_state.zoom_level = 100
if "sub_category" not in st.session_state:
    st.session_state.sub_category = "Select"
if "show_category_results" not in st.session_state:
    st.session_state.show_category_results = False
# Initialize main category selection
if "main_category" not in st.session_state:
    st.session_state.main_category = "Products"
# Track previous values for change detection
if "prev_main_category" not in st.session_state:
    st.session_state.prev_main_category = "Products"
if "prev_category" not in st.session_state:
    st.session_state.prev_category = ""
if "prev_sub_category" not in st.session_state:
    st.session_state.prev_sub_category = "Select"
# Track login status for warning message
if "fresh_login" not in st.session_state:
    st.session_state.fresh_login = False
if "warning_shown" not in st.session_state:
    st.session_state.warning_shown = False

# Check for fresh login from authentication redirect
query_params = st.query_params
if 'auth' in query_params and query_params['auth'] == 'success' and not st.session_state.warning_shown:
    st.session_state.fresh_login = True
    # Clear the auth parameter to prevent re-triggering on refresh
    st.query_params.clear()

# Debug information (temporary - can be removed later)
# st.write("DEBUG - Query params:", dict(query_params))
# st.write("DEBUG - Fresh login:", st.session_state.get('fresh_login', False))
# st.write("DEBUG - Warning shown:", st.session_state.get('warning_shown', False))


# --- Button mappings ---
button_names = [
    "Households 5L+", "Households 10L+", "Households 20L+", "Trade Area Analysis", 
    "Total Footfall", "Branded Footfall", "Hourly Footfall", "Daily Footfall", 
    "Weekday vs Weekend", "Category Analysis", "Customer Origins"
]
image_files  = [
    "YSHH5LA-1", "YSHH10LA-2", "YSHH20LA-3", "YSTA-4", 
    "YSTFF-5", "YSBFF-6", "YSHFF-7", "YSDFF-8", 
    "YSWWFF-9", "YSCM-10", "YSWSCF-11"
]

# --- Sub-category image mappings ---
sub_category_mapping = {
    "Exhaust Fans": "ExhaustFans",
    "Ceiling Fans": "CeilingFans",
    "Room Lights": "RoomLights",
    "Light Holders": "LightHolders",
    "Desk Lights": "DeskLights",
    "Solar Lights": "SolarLights",
    "Food Processor": "FoodProcessor",
    "Home Tools": "HomeTools",
    "BT SPK Sets / Radio": "BtSpkSetsRadio",
    "Ed Electronics": "EdElectronics",
    "Phone Accessories": "PhoneAccesories",
    "Electricians": "Electricians",
    "Plumbers": "Plumbers"
}

# --- Sidebar filters and buttons ---
with st.sidebar:
    st.markdown("## ️ Geographic Intelligence")

    # All Indian states and union territories
    all_states = [
        "Delhi(NCT)", "West Bengal", "Andhra Pradesh", "Arunachal Pradesh", "Assam",
        "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh",
        "Jharkhand", "Jammu and Kashmir", "Karnataka", "Kerala", "Ladakh",
        "Lakshadweep", "Madhya Pradesh", "Maharashtra", "Manipur", "Meghalaya",
        "Mizoram", "Nagaland", "Odisha", "Punjab", "Puducherry", "Rajasthan",
        "Sikkim", "Tamil Nadu", "Telangana", "Tripura", "Uttar Pradesh",
        "Uttarakhand", "Andaman & Nicobar Islands", "Chandigarh"
    ]

    selected_state = st.selectbox("State", all_states, key="state")

    # Enhanced geography options
    geography_options = ["East", "West", "North", "South", "Central", "South Central"]

    # Set default geography based on selected state
    if selected_state == "Delhi(NCT)":
        default_geography_index = 5  # "South Central"
    elif selected_state == "West Bengal":
        default_geography_index = 0  # "East"
    else:
        default_geography_index = 5  # Default to "South Central"

    selected_geography = st.selectbox("Geography", geography_options, index=default_geography_index, key="geography")

    # Dynamic locality based on state
    if selected_state == "Delhi(NCT)":
        locality_options = ["Yusuf Sarai", "East of Kailash", "RK Puram", "Saket", "Malviya Nagar", "Kidwai Nagar", "INA", "Adhchini", "SDA"]
    elif selected_state == "West Bengal":
        locality_options = ["South 24 Parganas"]
    else:
        locality_options = ["Yusuf Sarai", "East of Kailash", "RK Puram", "Saket", "Malviya Nagar", "Kidwai Nagar", "INA", "Adhchini", "SDA"]

    selected_locality = st.selectbox("Locality", locality_options, key="locality")

    # Store selected values in session state for use in other parts of the app
    st.session_state.selected_state = selected_state
    st.session_state.selected_geography = selected_geography
    st.session_state.selected_locality = selected_locality

    # Validate the combination of State, Geography, and Locality
    # Only Delhi(NCT) and West Bengal are valid states with specific combinations
    valid_combinations = [
        ("Delhi(NCT)", "South Central", "Yusuf Sarai"),
        ("West Bengal", "East", "South 24 Parganas")
    ]

    current_combination = (selected_state, selected_geography, selected_locality)
    is_valid_combination = current_combination in valid_combinations

    # Check if validation state has changed to trigger rerun
    previous_validation_state = st.session_state.get('is_valid_combination', True)

    # Store validation result in session state
    st.session_state.is_valid_combination = is_valid_combination

    # Real-time validation: Update error display state immediately
    if not is_valid_combination:
        st.session_state.show_realtime_error = True
    else:
        st.session_state.show_realtime_error = False

    # If validation state changed, trigger a rerun for immediate update
    # DISABLED: This was causing category selections to reset during validation failures
    # if previous_validation_state != is_valid_combination:
    #     st.rerun()

    st.markdown("## 📊 Analytics Views")
    for i in range(0, len(button_names), 2):
        cols = st.columns(2)
        for j in range(2):
            if i + j < len(button_names):
                if cols[j].button(button_names[i + j]):
                    st.session_state.selected_button = button_names[i + j]

    if st.button("📈 Detailed Analytics"):
        # Read the CSV file and convert to HTML table
        try:
            csv_df = pd.read_csv('DataValues-Dashboard-CSV.csv')

            # Convert Values column to float type and handle NaN values
            csv_df['Values'] = pd.to_numeric(csv_df['Values'], errors='coerce')
            # Only filter out NaN values, keep zero values
            csv_df = csv_df[csv_df['Values'].notna()]

            # Format Values column for display
            def format_value(val):
                if pd.isna(val):
                    return ''
                if val == 0:
                    return '0'
                elif val < 1 and val > 0:
                    formatted = f"{val:.5f}"
                elif val >= 1000:
                    formatted = f"{val:.0f}"
                else:
                    formatted = f"{val:.3f}"

                # Remove excessive trailing zeros, keep max one zero after decimal
                if '.' in formatted and val != 0:
                    formatted = formatted.rstrip('0')
                    if formatted.endswith('.'):
                        formatted += '0'
                    elif '.' not in formatted:
                        formatted += '.0'

                return formatted

            # Apply formatting to Values column for display
            csv_df['Values'] = csv_df['Values'].apply(format_value)

            # Get unique categories and subcategories for filters
            master_categories = sorted(csv_df['Master Category Name'].unique())
            all_subcategories = sorted(csv_df['Sub category'].unique())

            # Create subcategory mapping for dynamic filtering
            subcategory_mapping = {}
            for cat in master_categories:
                subcategory_mapping[cat] = sorted(csv_df[csv_df['Master Category Name'] == cat]['Sub category'].unique())

            # Encode logos for HTML
            logos_html_content = ""
            try:
                stonesbury_path = "logo/Stonesbury-logo.png"
                adobe_path = "logo/Adobe Express - file.png"

                if os.path.exists(stonesbury_path):
                    stonesbury_img = Image.open(stonesbury_path)
                    buffered = BytesIO()
                    stonesbury_img.save(buffered, format="PNG")
                    stonesbury_base64 = base64.b64encode(buffered.getvalue()).decode()
                    logos_html_content += f'<img src="data:image/png;base64,{stonesbury_base64}" alt="Stonesbury Logo" style="height: 45px; width: auto; margin-right: 20px; border: 1px solid #ddd; border-radius: 5px; padding: 5px; background: white;">'

                if os.path.exists(adobe_path):
                    adobe_img = Image.open(adobe_path)
                    buffered = BytesIO()
                    adobe_img.save(buffered, format="PNG")
                    adobe_base64 = base64.b64encode(buffered.getvalue()).decode()
                    logos_html_content += f'<img src="data:image/png;base64,{adobe_base64}" alt="Adobe Express Logo" style="height: 45px; width: auto; border: 1px solid #ddd; border-radius: 5px; padding: 5px; background: white;">'

            except Exception:
                # If logos fail to load, we'll use the fallback in HTML
                pass

            # Create HTML table with styling and filters
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            html_table = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Stonesburry Mimo Analytics - Detailed Data - {timestamp}</title>
                <style>
                    body {{
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        margin: 20px;
                        background-color: #f8f9fa;
                    }}
                    h1 {{
                        color: #ff6600;
                        text-align: center;
                        margin-bottom: 30px;
                    }}
                    .filter-container {{
                        background: white;
                        padding: 20px;
                        border-radius: 10px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        margin-bottom: 20px;
                        display: flex;
                        gap: 15px;
                        align-items: end;
                        flex-wrap: wrap;
                        justify-content: space-between;
                    }}
                    .filter-left {{
                        display: flex;
                        gap: 15px;
                        align-items: end;
                        flex-wrap: wrap;
                    }}
                    .filter-center {{
                        display: flex;
                        gap: 10px;
                        align-items: end;
                        flex-wrap: wrap;
                    }}
                    .filter-right {{
                        display: flex;
                        gap: 10px;
                        align-items: end;
                        flex-wrap: wrap;
                        margin-left: auto;
                    }}
                    .filter-group {{
                        display: flex;
                        flex-direction: column;
                        min-width: 180px;
                    }}
                    .filter-group label {{
                        color: #ff6600;
                        font-weight: bold;
                        margin-bottom: 5px;
                        font-size: 14px;
                    }}
                    .filter-group select {{
                        padding: 10px;
                        border: 2px solid #ff6600;
                        border-radius: 5px;
                        font-size: 14px;
                        background-color: white;
                        color: #333;
                    }}
                    .filter-group select:focus {{
                        outline: none;
                        border-color: #ff8533;
                        box-shadow: 0 0 5px rgba(255, 102, 0, 0.3);
                    }}
                    .search-group {{
                        display: flex;
                        flex-direction: column;
                        min-width: 250px;
                    }}
                    .search-group label {{
                        color: #ff6600;
                        font-weight: bold;
                        margin-bottom: 5px;
                        font-size: 14px;
                    }}
                    .search-group input {{
                        padding: 10px;
                        border: 2px solid #ff6600;
                        border-radius: 5px;
                        font-size: 14px;
                        background-color: white;
                        color: #333;
                    }}
                    .search-group input:focus {{
                        outline: none;
                        border-color: #ff8533;
                        box-shadow: 0 0 5px rgba(255, 102, 0, 0.3);
                    }}
                    .get-button {{
                        padding: 12px 25px;
                        background-color: #ff6600;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        font-size: 14px;
                        font-weight: bold;
                        cursor: pointer;
                        transition: background-color 0.3s ease;
                        height: fit-content;
                        min-width: 80px;
                    }}
                    .get-button:hover {{
                        background-color: #e55a00;
                    }}
                    .clear-button {{
                        padding: 12px 25px;
                        background-color: #6c757d;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        font-size: 14px;
                        font-weight: bold;
                        cursor: pointer;
                        transition: background-color 0.3s ease;
                        height: fit-content;
                        min-width: 80px;
                    }}
                    .clear-button:hover {{
                        background-color: #5a6268;
                    }}
                    .table-container {{
                        background: white;
                        border-radius: 10px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        overflow: hidden;
                        margin: 20px 0;
                    }}
                    table {{
                        width: 100%;
                        border-collapse: collapse;
                        font-size: 14px;
                    }}
                    th {{
                        background-color: #ff6600;
                        color: white;
                        padding: 12px 20px;
                        text-align: left;
                        font-weight: bold;
                        position: sticky;
                        top: 0;
                        z-index: 10;
                        min-width: 150px;
                    }}
                    th:first-child {{
                        width: 80px;
                        min-width: 80px;
                        text-align: center;
                    }}
                    td {{
                        padding: 10px 20px;
                        border-bottom: 1px solid #eee;
                        vertical-align: top;
                        min-width: 150px;
                        word-wrap: break-word;
                    }}
                    td:first-child {{
                        text-align: center;
                        font-weight: bold;
                        background-color: #f8f9fa;
                        color: #ff6600;
                        width: 80px;
                        min-width: 80px;
                    }}
                    tr:nth-child(even) td:first-child {{
                        background-color: #f0f0f0;
                    }}
                    tr:hover td:first-child {{
                        background-color: #fff3e0;
                    }}
                    /* Action Items column styling */
                    th:last-child {{
                        width: 120px;
                        min-width: 120px;
                        text-align: center;
                    }}
                    .action-cell {{
                        text-align: center;
                        padding: 8px 12px !important;
                        width: 120px;
                        min-width: 120px;
                    }}
                    .action-button {{
                        background: linear-gradient(135deg, #ff6600, #ff8533);
                        color: white;
                        border: none;
                        padding: 8px 12px;
                        border-radius: 5px;
                        font-size: 12px;
                        font-weight: bold;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        white-space: nowrap;
                        min-width: 90px;
                    }}
                    .action-button:hover {{
                        background: linear-gradient(135deg, #e55a00, #ff6600);
                        transform: translateY(-1px);
                        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                    }}
                    .action-button:active {{
                        transform: translateY(0);
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    }}
                    tr:nth-child(even) {{
                        background-color: #f8f9fa;
                    }}
                    tr:hover {{
                        background-color: #fff3e0;
                    }}
                    .stats {{
                        background: white;
                        padding: 20px;
                        border-radius: 10px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        margin-bottom: 20px;
                        text-align: center;
                    }}
                    .stats h3 {{
                        color: #ff6600;
                        margin: 0 0 10px 0;
                    }}

                    .no-results {{
                        text-align: center;
                        padding: 40px;
                        color: #666;
                        font-style: italic;
                    }}
                    .logo-container {{
                        position: static;
                        margin: 10px 0 20px 0;
                        display: flex;
                        align-items: center;
                        gap: 20px;
                        width: fit-content;
                    }}
                    .logo-container img {{
                        height: 45px;
                        width: auto;
                        display: block;
                    }}
                    .logo-fallback {{
                        height: 45px;
                        width: 120px;
                        background: #ff6600;
                        color: white;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-weight: bold;
                        border-radius: 3px;
                        font-size: 18px;
                    }}
                </style>
            </head>
            <body>
                <!-- Logo at top-left corner -->
                <div class="logo-container">
                    {logos_html_content if logos_html_content else '<div class="logo-fallback">GeoIQ</div>'}
                </div>

                <h1>Stonesburry Mimo Analytics Dashboard</h1>

                <!-- Filter and Search Section -->
                <div class="filter-container">
                    <div class="filter-left">
                        <div class="filter-group">
                            <label for="masterCategoryFilter">Master Category:</label>
                            <select id="masterCategoryFilter">
                                <option value="">All Categories</option>
                                {chr(10).join([f'<option value="{cat}">{cat}</option>' for cat in master_categories])}
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="subCategoryFilter">Sub Category:</label>
                            <select id="subCategoryFilter">
                                <option value="">All Sub-categories</option>
                                {chr(10).join([f'<option value="{subcat}">{subcat}</option>' for subcat in all_subcategories])}
                            </select>
                        </div>
                    </div>
                    <div class="filter-center">
                        <button class="get-button" onclick="applyFilters()">Go</button>
                        <button class="clear-button" onclick="clearFilters()">Clear</button>
                    </div>
                    <div class="filter-right">
                        <div class="search-group">
                            <label for="searchBox">Search:</label>
                            <input type="text" placeholder="Search data..." id="searchBox">
                        </div>
                    </div>
                </div>

                <div class="stats" id="statsContainer">
                    <h3>Data Summary</h3>
                    <p id="statsText"><strong>Total Records:</strong> {len(csv_df)} | <strong>Categories:</strong> {csv_df['Master Category Name'].nunique()} | <strong>Sub-categories:</strong> {csv_df['Sub category'].nunique()}</p>
                </div>

                <div class="table-container">
                    <div id="tableContent" style="display: none;">
                        <!-- Table will be populated when filters are applied -->
                    </div>
                    <div id="noResults" class="no-results" style="display: block;">
                        <h3>Please select filters to view data</h3>
                        <p>Choose a Master Category and/or Sub Category from the filters above, then click 'Go' to display the data.</p>
                    </div>
                </div>

                <script>
                    // Store original data for filtering
                    const originalData = {csv_df.to_json(orient='records')};
                    const subcategoryMapping = {subcategory_mapping};

                    // Test function to verify JavaScript is working
                    function testFunction() {{
                        console.log('JavaScript is working!');
                        alert('Test function called successfully!');
                    }}

                    // Simple function to open Mimo form
                    function openSampleForm(kpi, category, subcategory, row) {{
                        console.log('Opening Mimo form for KPI:', kpi);

                        // Encode parameters for URL
                        const params = new URLSearchParams({{
                            kpi: kpi || '',
                            category: category || '',
                            subcategory: subcategory || '',
                            row: row || ''
                        }});

                        // Construct URL with parameters
                        const baseUrl = 'http://localhost:8501/Mimo_Form';
                        const fullUrl = `${{baseUrl}}?${{params.toString()}}`;

                        console.log('Opening Mimo form with URL:', fullUrl);

                        // Open in new window with parameters
                        try {{
                            const newWindow = window.open('', '_blank');
                            if (newWindow) {{
                                newWindow.location.href = fullUrl;
                                console.log('Successfully opened Mimo form with parameters');
                            }} else {{
                                throw new Error('Popup blocked');
                            }}
                        }} catch (e) {{
                            console.log('Popup blocked or failed:', e);

                            // Alternative: show instructions with the full URL
                            alert(
                                'Please manually open the Mimo form:\\n\\n' +
                                '1. Open a new browser tab\\n' +
                                '2. Copy and paste this URL:\\n' +
                                `   ${{fullUrl}}\\n\\n` +
                                'Or use the Streamlit sidebar to navigate to Mimo_Form.'
                            );
                        }}
                    }}

                    // Update subcategory dropdown based on master category selection
                    document.getElementById('masterCategoryFilter').addEventListener('change', function() {{
                        const selectedCategory = this.value;
                        const subCategorySelect = document.getElementById('subCategoryFilter');

                        // Clear existing options
                        subCategorySelect.innerHTML = '<option value="">All Sub-categories</option>';

                        if (selectedCategory && subcategoryMapping[selectedCategory]) {{
                            subcategoryMapping[selectedCategory].forEach(subcat => {{
                                const option = document.createElement('option');
                                option.value = subcat;
                                option.textContent = subcat;
                                subCategorySelect.appendChild(option);
                            }});
                        }} else {{
                            // Show all subcategories if no master category selected
                            const allSubcats = {all_subcategories};
                            allSubcats.forEach(subcat => {{
                                const option = document.createElement('option');
                                option.value = subcat;
                                option.textContent = subcat;
                                subCategorySelect.appendChild(option);
                            }});
                        }}
                    }});

                    // Apply filters function
                    function applyFilters() {{
                        const masterCategory = document.getElementById('masterCategoryFilter').value;
                        const subCategory = document.getElementById('subCategoryFilter').value;

                        // Only show data if at least one filter is selected
                        if (!masterCategory && !subCategory) {{
                            document.getElementById('tableContent').style.display = 'none';
                            document.getElementById('noResults').style.display = 'block';
                            document.getElementById('noResults').innerHTML = '<h3>Please select filters to view data</h3><p>Choose a Master Category and/or Sub Category from the filters above to display the data.</p>';
                            return;
                        }}

                        let filteredData = originalData;

                        // Only filter out empty/null values, keep all formatted values including "0"
                        filteredData = filteredData.filter(row => {{
                            const value = row['Values'];
                            return value !== null && value !== undefined && value !== '';
                        }});

                        if (masterCategory) {{
                            filteredData = filteredData.filter(row => row['Master Category Name'] === masterCategory);
                        }}

                        if (subCategory) {{
                            filteredData = filteredData.filter(row => row['Sub category'] === subCategory);
                        }}

                        if (filteredData.length === 0) {{
                            document.getElementById('tableContent').style.display = 'none';
                            document.getElementById('noResults').style.display = 'block';
                            document.getElementById('noResults').innerHTML = '<h3>No data found</h3><p>No records match the selected filters. Please try different filter combinations.</p>';
                            return;
                        }}

                        // Show table and hide no results message
                        document.getElementById('tableContent').style.display = 'block';
                        document.getElementById('noResults').style.display = 'none';

                        // Generate table
                        generateTable(filteredData);

                        // Update stats
                        updateStats(filteredData);
                    }}

                    // Generate table function
                    function generateTable(data) {{
                        const tableContent = document.getElementById('tableContent');

                        if (data.length === 0) {{
                            tableContent.innerHTML = '<p class="no-results">No data available for the selected filters.</p>';
                            return;
                        }}

                        // Get all unique columns from the data, ensuring Action Items is last
                        const allColumns = [...new Set(data.flatMap(row => Object.keys(row)))];
                        const columns = allColumns.filter(col => col !== 'Action Items');
                        columns.push('Action Items'); // Add Action Items as the last column

                        let tableHTML = '<table><thead><tr>';
                        columns.forEach(col => {{
                            tableHTML += `<th>${{col}}</th>`;
                        }});
                        tableHTML += '</tr></thead><tbody>';

                        if (data && data.length > 0) {{
                            data.forEach((row, index) => {{
                                const rowClass = index % 2 === 0 ? '' : 'even';
                                tableHTML += `<tr class="${{rowClass}}">`;
                                columns.forEach(col => {{
                                    if (col === 'Action Items') {{
                                        // Create unique button for each row with KPI Number and category info
                                        const kpiNumber = row['KPI Number'] || index + 1;
                                        const category = row['Master Category Name'] || 'Unknown';
                                        const subCategory = row['Sub category'] || 'Unknown';
                                        const buttonId = `action_btn_${{kpiNumber}}_${{index}}`;

                                        tableHTML += `<td class="action-cell">
                                            <button class="action-button"
                                                    id="${{buttonId}}"
                                                    onclick="openSampleForm('${{kpiNumber}}', '${{category}}', '${{subCategory}}', '${{index}}')">
                                                View Details
                                            </button>
                                        </td>`;
                                    }} else {{
                                        let cellValue = row[col] || '';
                                        // Values are already formatted in Python, just use them as-is
                                        // No additional formatting needed since Python handles the display formatting
                                        tableHTML += `<td>${{cellValue}}</td>`;
                                    }}
                                }});
                                tableHTML += '</tr>';
                            }});

                            tableHTML += '</tbody></table>';
                            tableContent.innerHTML = tableHTML;
                        }}
                    }}

                    // Update stats
                    function updateStats(data) {{
                        const uniqueCategories = [...new Set(data.map(row => row['Master Category Name']))].length;
                        const uniqueSubcategories = [...new Set(data.map(row => row['Sub category']))].length;

                        document.getElementById('statsText').innerHTML =
                            `<strong>Total Records:</strong> ${{data.length}} | <strong>Categories:</strong> ${{uniqueCategories}} | <strong>Sub-categories:</strong> ${{uniqueSubcategories}}`;
                    }}

                    // Clear filters function
                    function clearFilters() {{
                        document.getElementById('masterCategoryFilter').value = '';
                        document.getElementById('subCategoryFilter').value = '';
                        document.getElementById('searchBox').value = '';

                        // Reset subcategory dropdown to show all options
                        const subCategorySelect = document.getElementById('subCategoryFilter');
                        subCategorySelect.innerHTML = '<option value="">All Sub-categories</option>';
                        const allSubcats = {all_subcategories};
                        allSubcats.forEach(subcat => {{
                            const option = document.createElement('option');
                            option.value = subcat;
                            option.textContent = subcat;
                            subCategorySelect.appendChild(option);
                        }});

                        // Hide table and show initial message
                        document.getElementById('tableContent').style.display = 'none';
                        document.getElementById('noResults').style.display = 'block';
                        document.getElementById('noResults').innerHTML = '<h3>Please select filters to view data</h3><p>Choose a Master Category and/or Sub Category from the filters above, then click \\'Go\\' to display the data.</p>';

                        // Reset stats
                        document.getElementById('statsText').innerHTML = `<strong>Total Records:</strong> {len(csv_df)} | <strong>Categories:</strong> {csv_df['Master Category Name'].nunique()} | <strong>Sub-categories:</strong> {csv_df['Sub category'].nunique()}`;
                    }}

                    // Search functionality
                    document.getElementById('searchBox').addEventListener('input', function() {{
                        const searchTerm = this.value.toLowerCase();
                        const tableRows = document.querySelectorAll('#tableContent tbody tr');

                        tableRows.forEach(row => {{
                            const rowText = row.textContent.toLowerCase();
                            if (rowText.includes(searchTerm)) {{
                                row.style.display = '';
                            }} else {{
                                row.style.display = 'none';
                            }}
                        }});
                    }});
                </script>
            </body>
            </html>
            """

            # Encode the HTML to base64 for the data URL
            import base64
            encoded_html = base64.b64encode(html_table.encode()).decode()

            # JavaScript to open new tab with the HTML table
            js = f"""
            var newWindow = window.open();
            newWindow.document.write(atob('{encoded_html}'));
            newWindow.document.close();
            """
            st.components.v1.html(f"<script>{js}</script>", height=0)

        except FileNotFoundError:
            st.error("❌ DataValues-Dashboard-CSV.csv file not found!")
        except Exception as e:
            st.error(f"❌ Error loading data: {str(e)}")

    # Main Menu (Products/Services)
    st.markdown("## 🏷️ Business Category")

    main_category = st.radio("Select Business Type", ["Products", "Services"], key="main_category", label_visibility="collapsed")

    # Reset results display if main category changes (but preserve the selection during validation failures)
    if st.session_state.prev_main_category != main_category:
        st.session_state.show_category_results = False
        st.session_state.prev_main_category = main_category
        # Clear sub-category selection when switching main categories
        if "sub_category" in st.session_state:
            st.session_state.sub_category = "Select"
        # Reset category selection when switching main categories
        if "category" in st.session_state:
            # Auto-set to "Services" if switching to Services, otherwise clear
            if main_category == "Services":
                st.session_state.category = "Services"
            else:
                del st.session_state.category  # Let the selectbox choose default

    st.markdown("---")

    # Product & Service Categories Button
    if st.button("🏷️ Product & Service Categories", use_container_width=True):
        # Toggle the visibility state
        if "show_categories" not in st.session_state:
            st.session_state.show_categories = True
        else:
            st.session_state.show_categories = not st.session_state.show_categories

        # Reset results display and tracking variables when toggling the categories section
        st.session_state.show_category_results = False
        st.session_state.prev_main_category = st.session_state.get("main_category", "Products")
        st.session_state.prev_category = st.session_state.get("category", "")
        st.session_state.prev_sub_category = st.session_state.get("sub_category", "Select")

    # Brand search functionality
    st.markdown("**Brand Search:**")

    # Define brand options
    brand_options = [
        "All Brands",
        "Fab India",
        "Croma",
        "Nike",
        "Hero Cycles",
        "Patanjali",
        "Shoppers Stop",
        "Orient",
        "ICICI",
        "HDFC",
        "Yes Bank",
        "Kawasaki",
        "TVS",
        "IRCTC",
        "DMI",
        "Westside",
        "Joyalukkas",
        "Naturals",
        "Amaron"
    ]

    brand_search_input = st.selectbox(
        "Select brand to filter images:",
        options=brand_options,
        index=0,  # Default to "All Brands"
        key="main_brand_search",
        label_visibility="collapsed"
    )

    # Store brand search input for Interactive Map
    if brand_search_input and brand_search_input != "All Brands":
        st.session_state.map_search_query = brand_search_input
    else:
        # Clear the search query if "All Brands" is selected
        st.session_state.map_search_query = ""

    # Open button to launch the Interactive Map page
    if st.button("Open", use_container_width=True):
        # Get the current selected state, geography, and locality
        current_selected_state = st.session_state.get("state", "Delhi(NCT)")
        current_selected_geography = st.session_state.get("geography", "South Central")
        current_selected_locality = st.session_state.get("locality", "Yusuf Sarai")

        # Store the selected values in session state for the map page
        st.session_state.selected_map_state = current_selected_state
        st.session_state.selected_map_geography = current_selected_geography
        st.session_state.selected_map_locality = current_selected_locality

        # Redirect to the Interactive Map page
        st.switch_page("pages/Interactive_Map.py")

# --- Add logo with current validation after sidebar processing ---
add_logo_native()

# --- Add timestamp display above dashboard content ---
add_timestamp_display()

# --- Debug Information (temporary) ---
# st.write("DEBUG INFO:")
# st.write(f"main_category: {st.session_state.get('main_category', 'Not Set')}")
# st.write(f"prev_main_category: {st.session_state.get('prev_main_category', 'Not Set')}")
# st.write(f"category: {st.session_state.get('category', 'Not Set')}")
# st.write(f"sub_category: {st.session_state.get('sub_category', 'Not Set')}")
# st.write(f"is_valid_combination: {st.session_state.get('is_valid_combination', True)}")
# st.write(f"show_categories: {st.session_state.get('show_categories', False)}")
# st.write(f"current_combination: {(selected_state, selected_geography, selected_locality)}")

# --- Main image display section ---
col1, col2 = st.columns([1, 5])

# --- Category and Subcategory Container in col1 ---
with col1:
    st.markdown("""
        <style>
            .category-container {
                background: linear-gradient(145deg, #ffffff, #f8f9fa);
                padding: 20px;
                border-radius: 10px;
                border: 2px solid #ff6600;
                margin: 10px 0;
                height: fit-content;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
            .category-header {
                color: #ff6600 !important;
                font-size: 18px !important;
                font-weight: bold !important;
                margin-bottom: 15px !important;
                text-align: center;
                border-bottom: 1px solid #ff6600;
                padding-bottom: 10px;
            }
        </style>
    """, unsafe_allow_html=True)
    

    # Show Product & Service Categories only if button was clicked
    if st.session_state.get("show_categories", False):
        with st.container():
            # st.markdown('<div class="category-container">', unsafe_allow_html=True)
            st.markdown('<h4 class="category-header">Product & Service Categories</h4>', unsafe_allow_html=True)

            # Get current selected state for state-specific options
            current_state = st.session_state.get("state", "Delhi(NCT)")

            # Dictionary of subcategory options (state-specific)
            if current_state == "Delhi(NCT)":
                subcategory_options = {
                    "Products": {
                        "Electronics & H. Appliances": [
                            "Select", "Exhaust Fans", "Ceiling Fans", "Room Lights", "Light Holders",
                            "Desk Lights", "Solar Lights", "Food Processor", "Home Tools",
                            "BT SPK Sets / Radio", "Ed Electronics", "Phone Accessories"
                        ],
                        "Fashion & Apparel": [
                            "Select", "Clothing", "Accessories", "Shoes", "Bags"
                        ],
                        "Jewelry": [
                            "Select", "Gold Jewelry", "Silver Jewelry", "Fashion Jewelry", "Watches"
                        ],
                        "Home Furnishing": [
                            "Select", "Furniture", "Curtains", "Bedding", "Decor Items"
                        ],
                        "Footwear": [
                            "Select", "Formal Shoes", "Casual Shoes", "Sports Shoes", "Sandals"
                        ],
                        "Food & Service": [
                            "Select", "Packaged Food", "Fresh Food", "Beverages", "Snacks"
                        ],
                        "Luggage": [
                            "Select", "Suitcases", "Travel Bags", "Backpacks", "Handbags"
                        ]
                    },
                    "Services": {
                        "Services": ["Electricians", "Mason", "Lawyer", "Welder", "Auto mechanic", "Plumbers"],
                        "Restaurant": [
                            "Select", "Fine Dining", "Fast Food", "Cafe", "Street Food"
                        ]
                    }
                }
            elif current_state == "West Bengal":
                subcategory_options = {
                    "Products": {
                        "Electronics & H. Appliances": [
                            "Select", "Exhaust Fans", "Ceiling Fans", "Room Lights", "Light Holders",
                            "Desk Lights", "Solar Lights", "Food Processor", "Home Tools",
                            "BT SPK Sets / Radio", "Ed Electronics", "Phone Accessories"
                        ],
                        "Fashion & Apparel": [
                            "Select", "Clothing", "Accessories", "Shoes", "Bags"
                        ],
                        "Jewelry": [
                            "Select", "Gold Jewelry", "Silver Jewelry", "Fashion Jewelry", "Watches"
                        ],
                        "Home Furnishing": [
                            "Select", "Furniture", "Curtains", "Bedding", "Decor Items"
                        ],
                        "Footwear": [
                            "Select", "Formal Shoes", "Casual Shoes", "Sports Shoes", "Sandals"
                        ],
                        "Food & Service": [
                            "Select", "Packaged Food", "Fresh Food", "Beverages", "Snacks"
                        ],
                        "Luggage": [
                            "Select", "Suitcases", "Travel Bags", "Backpacks", "Handbags"
                        ]
                    },
                    "Services": {
                        "Services": ["Electricians", "Mason", "Lawyer", "Welder", "Auto mechanic", "Plumbers"],
                        "Restaurant": [
                            "Select", "Fine Dining", "Fast Food", "Cafe", "Street Food"
                        ]
                    }
                }
            else:
                # Default to Delhi options
                subcategory_options = {
                    "Products": {
                        "Electronics & H. Appliances": [
                            "Select", "Exhaust Fans", "Ceiling Fans", "Room Lights", "Light Holders",
                            "Desk Lights", "Solar Lights", "Food Processor", "Home Tools",
                            "BT SPK Sets / Radio", "Ed Electronics", "Phone Accessories"
                        ],
                        "Fashion & Apparel": [
                            "Select", "Clothing", "Accessories", "Shoes", "Bags"
                        ],
                        "Jewelry": [
                            "Select", "Gold Jewelry", "Silver Jewelry", "Fashion Jewelry", "Watches"
                        ],
                        "Home Furnishing": [
                            "Select", "Furniture", "Curtains", "Bedding", "Decor Items"
                        ],
                        "Footwear": [
                            "Select", "Formal Shoes", "Casual Shoes", "Sports Shoes", "Sandals"
                        ],
                        "Food & Service": [
                            "Select", "Packaged Food", "Fresh Food", "Beverages", "Snacks"
                        ],
                        "Luggage": [
                            "Select", "Suitcases", "Travel Bags", "Backpacks", "Handbags"
                        ]
                    },
                    "Services": {
                        "Services": ["Electricians", "Mason", "Lawyer", "Welder", "Auto mechanic", "Plumbers"],
                        "Restaurant": [
                            "Select", "Fine Dining", "Fast Food", "Cafe", "Street Food"
                        ]
                    }
                }

            # Category and Subcategory selection
            # Use the actual radio button value, with fallback for safety
            main_category = st.session_state.get("main_category", "Products")

            if main_category in subcategory_options:
                st.markdown("**Category:**")

                # Get available categories for the selected main category
                available_categories = list(subcategory_options[main_category].keys())

                # Auto-select "Services" if main category is "Services" and category is not set
                if main_category == "Services" and st.session_state.get("category") not in available_categories:
                    default_index = available_categories.index("Services") if "Services" in available_categories else 0
                else:
                    # Try to maintain current selection if valid
                    current_category = st.session_state.get("category")
                    default_index = available_categories.index(current_category) if current_category in available_categories else 0

                category = st.selectbox(
                    "Choose Category",
                    available_categories,
                    index=default_index,
                    key="category",
                    label_visibility="collapsed"
                )

                # Reset results display if category changes
                if st.session_state.prev_category != category:
                    st.session_state.show_category_results = False
                    st.session_state.prev_category = category

                if category:
                    st.markdown("**Product/Service Type:**")
                    subcategories = subcategory_options[main_category][category]

                    current_sub_category = st.selectbox(
                        "Choose Specific Type",
                        subcategories,
                        key="sub_category",
                        label_visibility="collapsed"
                    )

                    # Reset results display if sub-category changes
                    if st.session_state.prev_sub_category != current_sub_category:
                        st.session_state.show_category_results = False
                        st.session_state.prev_sub_category = current_sub_category

                    # Validate Product/Service Type selection based on state
                    current_state = st.session_state.get("state", "Delhi(NCT)")

                    # Get the base geographical validation
                    selected_state = st.session_state.get("state", "Delhi(NCT)")
                    selected_geography = st.session_state.get("geography", "South Central")
                    selected_locality = st.session_state.get("locality", "Yusuf Sarai")

                    valid_geographical_combinations = [
                        ("Delhi(NCT)", "South Central", "Yusuf Sarai"),
                        ("West Bengal", "East", "South 24 Parganas")
                    ]

                    current_geographical_combination = (selected_state, selected_geography, selected_locality)
                    is_valid_geographical = current_geographical_combination in valid_geographical_combinations

                    # Apply category-specific validation
                    if main_category == "Services" and category == "Services" and current_sub_category and current_sub_category != "Select":
                        # Service validation: Check service type validity based on state
                        if current_state == "Delhi(NCT)":
                            # For Delhi, only Electricians is valid
                            is_valid_service_selection = current_sub_category == "Electricians"
                        elif current_state == "West Bengal":
                            # For West Bengal, only Electricians and Plumbers are valid
                            is_valid_service_selection = current_sub_category in ["Electricians", "Plumbers"]
                        else:
                            # For other states, only Electricians is valid (default behavior)
                            is_valid_service_selection = current_sub_category == "Electricians"

                        # Combine geographical and service validation
                        new_validation_state = is_valid_geographical and is_valid_service_selection
                    elif main_category == "Products" and category and category != "Electronics & H. Appliances":
                        # Product validation: Only "Electronics & H. Appliances" is valid for Products
                        # All other product categories should show error
                        new_validation_state = False
                    else:
                        # If no specific validation applies, use geographical validation
                        new_validation_state = is_valid_geographical

                    # Update validation state (but don't trigger reruns that reset the interface)
                    st.session_state.is_valid_combination = new_validation_state

                    # Add "Show Results" button below the dropdown
                    st.markdown("<br>", unsafe_allow_html=True)  # Add some spacing
                    if st.button("Go", key="show_category_results_btn", use_container_width=True):
                        st.session_state.show_category_results = True

            st.markdown('</div>', unsafe_allow_html=True)
    else:
        # Reset results when categories section is hidden
        if st.session_state.get("show_category_results", False):
            st.session_state.show_category_results = False

        # Reset validation to geographical validation only when categories are hidden
        geographical_validation = st.session_state.get('is_valid_combination', True)
        # Check if we need to restore geographical validation
        selected_state = st.session_state.get("state", "Delhi(NCT)")
        selected_geography = st.session_state.get("geography", "South Central")
        selected_locality = st.session_state.get("locality", "Yusuf Sarai")

        valid_geographical_combinations = [
            ("Delhi(NCT)", "South Central", "Yusuf Sarai"),
            ("West Bengal", "East", "South 24 Parganas")
        ]

        current_geographical_combination = (selected_state, selected_geography, selected_locality)
        is_valid_geographical = current_geographical_combination in valid_geographical_combinations
        st.session_state.is_valid_combination = is_valid_geographical

with col2:
    image_rendered = False

    # Check validation state first - if invalid, show error and skip all image rendering
    is_valid = st.session_state.get('is_valid_combination', True)

    if not is_valid:
        # Show error message for invalid combination in a box with bright red text
        st.markdown("""
        <div style="text-align: center; padding: 100px; border: 2px dashed #ff6600; border-radius: 10px; background: linear-gradient(145deg, #ffffff, #f8f9fa); box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);">
            <h3 id="main-error-text" class="error-message-red" style="color: #DC143C !important; font-size: 32px !important; font-weight: bold !important; margin: 0 !important; text-shadow: 1px 1px 2px rgba(0,0,0,0.1);">Libraries are not connected</h3>
        </div>
        """, unsafe_allow_html=True)

        # Add CSS styling for bright red color
        st.markdown("""
        <style>
        .error-message-red {
            color: #DC143C !important;
            font-size: 32px !important;
            font-weight: bold !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1) !important;
        }
        #main-error-text {
            color: #DC143C !important;
            font-size: 32px !important;
            font-weight: bold !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1) !important;
        }
        /* Override any Streamlit default colors */
        .stMarkdown h3 {
            color: #DC143C !important;
        }
        </style>
        """, unsafe_allow_html=True)

        # Add JavaScript to force bright red color
        st.markdown("""
        <script>
        function forceMainErrorRed() {
            const errorText = document.getElementById('main-error-text');

            if (errorText) {
                errorText.style.setProperty('color', '#DC143C', 'important');
                errorText.style.setProperty('font-size', '32px', 'important');
                errorText.style.setProperty('font-weight', 'bold', 'important');
                errorText.style.setProperty('margin', '0', 'important');
                errorText.style.setProperty('text-shadow', '1px 1px 2px rgba(0,0,0,0.1)', 'important');
            }

            // Also target any elements containing the error text
            const allElements = document.querySelectorAll('*');
            allElements.forEach(el => {
                if (el.textContent && el.textContent.includes('Libraries are not connected')) {
                    el.style.setProperty('color', '#DC143C', 'important');
                }
            });

            // Force bright red color using CSS
            const style = document.createElement('style');
            style.textContent = `
                #main-error-text {
                    color: #DC143C !important;
                    font-size: 32px !important;
                    font-weight: bold !important;
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.1) !important;
                }
                .error-message-red {
                    color: #DC143C !important;
                    font-size: 32px !important;
                    font-weight: bold !important;
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.1) !important;
                }
                h3 {
                    color: #DC143C !important;
                }
            `;
            document.head.appendChild(style);
        }

        // Run immediately and keep trying
        setTimeout(forceMainErrorRed, 100);
        setTimeout(forceMainErrorRed, 500);
        setTimeout(forceMainErrorRed, 1000);

        // Keep trying for a few seconds
        let attempts = 0;
        const interval = setInterval(() => {
            forceMainErrorRed();
            attempts++;
            if (attempts >= 30) {
                clearInterval(interval);
            }
        }, 200);
        </script>
        """, unsafe_allow_html=True)

        image_rendered = True  # Set to True to prevent further processing

    # Priority 1: Button-based static images (only if validation is valid)
    elif st.session_state.selected_button:
        selected_label = st.session_state.selected_button

        # Regular static image handling
        index = button_names.index(selected_label)
        image_name = image_files[index]

        # Get the current selected state and determine the appropriate image folder
        current_state = st.session_state.get("state", "Delhi(NCT)")
        image_folder = get_image_folder_by_state(current_state)
        image_path = os.path.join(image_folder, f"{image_name}.png")

        if os.path.exists(image_path):
            # Load image with enhanced quality settings
            image = Image.open(image_path)

            # Enhance image quality for better zoom experience
            # Increase resolution if image is small
            original_width, original_height = image.size
            if original_width < 1200 or original_height < 800:
                # Scale up smaller images for better zoom quality
                scale_factor = max(1200 / original_width, 800 / original_height)
                new_width = int(original_width * scale_factor)
                new_height = int(original_height * scale_factor)
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            encoded_image = image_to_base64(image, quality=98)

            st.markdown(f"### 📊 {selected_label}")

            # Display the enhanced zoomable image
            zoom_html = render_zoomable_image(
                encoded_image,
                selected_label,
                st.session_state.zoom_level,
                "main_image"
            )
            st.components.v1.html(zoom_html, height=650)

            # Enhanced zoom controls in a styled container (positioned below image)
            with st.container():
                st.markdown('<div class="zoom-control-section">', unsafe_allow_html=True)
                render_zoom_controls("main")
                st.markdown('</div>', unsafe_allow_html=True)

            image_rendered = True
        else:
            st.error(f"❌ Image '{image_name}.png' not found in folder '{image_folder}'.")

    # Priority 3: Sub-Category image (only if no live map or button image shown AND Show Results button was clicked)
    elif not image_rendered and st.session_state.get("show_category_results", False):
        subcat_name = st.session_state.get("sub_category", "Select")
        image_filename = sub_category_mapping.get(subcat_name)

        if image_filename and subcat_name != "Select":
            # Get the current selected state and determine the appropriate item image folder
            current_state = st.session_state.get("state", "Delhi(NCT)")
            image_folder = get_item_image_folder_by_state(current_state)
            image_path = os.path.join(image_folder, f"{image_filename}.png")

            if os.path.exists(image_path):
                # Load sub-category image with enhanced quality settings
                image = Image.open(image_path)

                # Enhance image quality for better zoom experience
                original_width, original_height = image.size
                if original_width < 1200 or original_height < 800:
                    # Scale up smaller images for better zoom quality
                    scale_factor = max(1200 / original_width, 800 / original_height)
                    new_width = int(original_width * scale_factor)
                    new_height = int(original_height * scale_factor)
                    image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

                encoded_image = image_to_base64(image, quality=98)

                st.markdown(f"### 🛍️ {subcat_name}")

                # Display the enhanced zoomable image
                zoom_html = render_zoomable_image(
                    encoded_image,
                    subcat_name,
                    st.session_state.zoom_level,
                    "subcat_image"
                )
                st.components.v1.html(zoom_html, height=650)

                # Enhanced zoom controls for sub-category images (positioned below image)
                with st.container():
                    st.markdown('<div class="zoom-control-section">', unsafe_allow_html=True)
                    render_zoom_controls("subcat")
                    st.markdown('</div>', unsafe_allow_html=True)

                image_rendered = True
            else:
                st.error(f"❌ Sub-category image '{image_filename}.png' not found in folder '{image_folder}'.")
        elif subcat_name == "Select" and st.session_state.get("show_category_results", False):
            # Show message when Show Results is clicked but no specific sub-category is selected
            st.info("🔍 Please select a specific Product/Service Type from the dropdown above to view results.")

    # Default welcome message when no image is selected and validation is valid
    if not image_rendered and is_valid:
        # Show default welcome message for valid combinations
        st.markdown("""
        <div style="text-align: center; padding: 100px; border: 2px dashed #ff6600; border-radius: 10px; background: linear-gradient(145deg, #ffffff, #f8f9fa); box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);">
            <h3 style="color: #ff6600;">Welcome to Stonesbury Mimo Analytics Dashboard</h3>
            <p style="color: #666;">Select an analytics view from the sidebar to explore geographic insights, or choose a product/service category to view detailed market information.</p>
        </div>
        """, unsafe_allow_html=True)
