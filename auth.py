import streamlit as st
import webbrowser
import os
import time
from datetime import datetime, timedelta
import subprocess
import sys

def check_authentication():
    """
    Check if user is authenticated by looking for authentication markers.
    Returns True if authenticated, False otherwise.
    """
    # Check session state first
    if 'authenticated' in st.session_state and st.session_state.authenticated:
        return True
    
    # Check if we have authentication parameters in URL or session
    query_params = st.query_params
    if 'auth' in query_params and query_params['auth'] == 'success':
        st.session_state.authenticated = True
        st.session_state.user_email = query_params.get('email', '<EMAIL>')
        st.session_state.login_time = datetime.now()
        return True
    
    return False

def require_authentication():
    """
    Require authentication before allowing access to the app.
    If not authenticated, redirect to login page.
    """
    if not check_authentication():
        show_login_redirect()
        st.stop()

def show_login_redirect():
    """
    Show login redirect page and open login.html in browser.
    """
    # Note: st.set_page_config should already be called by the main app
    
    st.markdown("""
        <style>
            .main-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                min-height: 70vh;
                text-align: center;
            }
            .login-card {
                background: white;
                padding: 2rem;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                border: 2px solid #ff6600;
                max-width: 500px;
                margin: 0 auto;
            }
            .login-title {
                color: #ff6600;
                font-size: 2rem;
                margin-bottom: 1rem;
            }
            .login-message {
                color: #333;
                font-size: 1.1rem;
                margin-bottom: 1.5rem;
                line-height: 1.6;
            }
            .login-button {
                background-color: #ff6600;
                color: white;
                padding: 12px 30px;
                border: none;
                border-radius: 5px;
                font-size: 1.1rem;
                font-weight: bold;
                cursor: pointer;
                text-decoration: none;
                display: inline-block;
                transition: background-color 0.3s ease;
            }
            .login-button:hover {
                background-color: #e55a00;
                text-decoration: none;
                color: white;
            }
            .instructions {
                background-color: #f8f9fa;
                padding: 1rem;
                border-radius: 5px;
                margin-top: 1rem;
                border-left: 4px solid #ff6600;
            }
        </style>
        
        <div class="main-container">
            <div class="login-card">
                <h1 class="login-title">🔐 Authentication Required</h1>
                <p class="login-message">
                    Please log in to access the GeoIQ Analytics Dashboard.
                    <br><br>
                    Click the button below to open the login page.
                </p>
                <div class="instructions">
                    <strong>Login Credentials:</strong><br>
                    Email: <EMAIL><br>
                    Password: user
                </div>
            </div>
        </div>
    """, unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        if st.button("🚀 Open Login Page", key="open_login", use_container_width=True):
            open_login_page()
            st.info("Login page opened in your browser. Please complete the login process.")
            time.sleep(2)
            st.rerun()
    
    # Auto-refresh to check for authentication
    time.sleep(3)
    st.rerun()

def open_login_page():
    """
    Open the login.html file in the default browser.
    """
    login_file_path = os.path.abspath("login.html")
    
    if os.path.exists(login_file_path):
        try:
            # Try different methods to open the browser
            if sys.platform.startswith('win'):
                os.startfile(login_file_path)
            elif sys.platform.startswith('darwin'):  # macOS
                subprocess.run(['open', login_file_path])
            else:  # Linux and others
                subprocess.run(['xdg-open', login_file_path])
        except Exception as e:
            st.error(f"Could not open login page automatically: {e}")
            st.info(f"Please manually open: {login_file_path}")
    else:
        st.error("Login file not found. Please ensure login.html exists in the project directory.")

def logout():
    """
    Log out the current user.
    """
    # Clear session state
    if 'authenticated' in st.session_state:
        del st.session_state.authenticated
    if 'user_email' in st.session_state:
        del st.session_state.user_email
    if 'login_time' in st.session_state:
        del st.session_state.login_time
    
    # Clear query parameters
    st.query_params.clear()
    
    st.success("Logged out successfully!")
    time.sleep(1)
    st.rerun()

def get_user_info():
    """
    Get current user information.
    """
    if check_authentication():
        return {
            'email': st.session_state.get('user_email', 'Unknown'),
            'login_time': st.session_state.get('login_time', datetime.now()),
            'authenticated': True
        }
    return {'authenticated': False}

def show_user_info():
    """
    Show user information in sidebar.
    """
    user_info = get_user_info()
    if user_info['authenticated']:
        st.sidebar.markdown("---")
        st.sidebar.markdown("### 👤 User Info")
        st.sidebar.info(f"**Email:** {user_info['email']}")
        if st.sidebar.button("🚪 Logout"):
            logout()
