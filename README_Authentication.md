# GeoIQ Analytics Dashboard - Authentication Setup

## Overview
The GeoIQ Analytics Dashboard now includes an authentication system that requires users to log in before accessing the main application.

## How It Works

### Authentication Flow
1. **Startup**: When you run the main application, it automatically opens the login page in your browser
2. **Login**: Enter your credentials on the login page
3. **Redirect**: After successful login, you're automatically redirected to the main dashboard
4. **Session**: Your login session is maintained for 24 hours

### Login Credentials
- **Email**: `<EMAIL>`
- **Password**: `user1234`

## How to Start the Application

### Method 1: Using the Startup Script (Recommended)
```bash
python start_app.py
```

### Method 2: Using the Batch File (Windows)
Double-click `start_app.bat`

### Method 3: Manual Startup
1. First, open `login.html` in your browser
2. Then run: `streamlit run POC-10J-17.py`

## Files Added/Modified

### New Files
- `auth.py` - Authentication module for Streamlit
- `start_app.py` - Startup script that opens login page and starts Streamlit
- `start_app.bat` - Windows batch file for easy startup
- `README_Authentication.md` - This documentation

### Modified Files
- `login.html` - Fixed JavaScript bugs and added proper redirect logic
- `POC-10J-17.py` - Added authentication check and user info display

## Features

### Authentication Features
- ✅ Login page opens automatically when starting the app
- ✅ Secure credential validation
- ✅ Session management (24-hour expiry)
- ✅ Automatic redirect to dashboard after login
- ✅ User info display in sidebar
- ✅ Logout functionality
- ✅ Remember login state across browser sessions

### Security Features
- Session timeout after 24 hours
- Local storage for session management
- Authentication required before accessing any dashboard features
- Clean logout process

## Troubleshooting

### Common Issues

1. **Login page doesn't open automatically**
   - Manually open `login.html` in your browser
   - Check if your default browser is set correctly

2. **Streamlit app doesn't start**
   - Ensure you have Streamlit installed: `pip install streamlit`
   - Check if port 8501 is available
   - Try running manually: `streamlit run POC-10J-17.py`

3. **Authentication not working**
   - Clear your browser's local storage
   - Try refreshing the page
   - Ensure you're using the correct credentials

4. **Redirect issues**
   - Make sure Streamlit is running on port 8501
   - Check browser console for any JavaScript errors

### Manual Reset
If you need to reset the authentication:
1. Clear browser local storage
2. Restart the application
3. Log in again

## Technical Details

### Session Management
- Authentication status is stored in browser's localStorage
- Session includes: authentication flag, user email, and login timestamp
- Automatic session validation on page load
- 24-hour session expiry

### URL Parameters
The login page passes authentication information via URL parameters:
- `auth=success` - Indicates successful authentication
- `email=<EMAIL>` - User's email address

### File Structure
```
├── login.html              # Login page
├── auth.py                 # Authentication module
├── start_app.py           # Startup script
├── start_app.bat          # Windows batch file
├── POC-10J-17.py          # Main Streamlit application
└── README_Authentication.md # This file
```

## Support
If you encounter any issues with the authentication system, please check the troubleshooting section above or contact support.
