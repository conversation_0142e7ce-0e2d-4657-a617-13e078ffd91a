<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Login - Orange Gradient</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="min-h-screen flex flex-col md:flex-row relative">



  <!-- Left Image Side -->
  <div class="hidden md:flex w-full md:w-1/2 h-screen bg-cover bg-center relative"
       style="background-image: url('log.png');">
    <!-- Orange Overlay -->
    <div class="absolute inset-0 bg-gradient-to-br from-orange-500 via-orange-600 to-orange-700 opacity-10"></div>

    <!-- Bottom Text -->
    <div class="absolute bottom-0 p-10 text-white z-10">
      <h3 class="text-2xl font-bold mb-1">Bring your ideas to life.</h3>
      <p class="text-sm">Sign up for free and enjoy access to all features for 30 days. No credit card required.</p>
    </div>
  </div>

  <!-- Right Side: Form Section -->
  <div class="w-full md:w-1/2 bg-white relative flex flex-col items-center justify-center px-8 py-12">
    
    <!-- Adobe Logo above form, aligned left inside the right section -->
    <!-- Top Bar: Stonesbury (Left) and Adobe (Right) -->
<div class="absolute top-6 left-6 right-6 z-50 flex justify-between items-center px-6">
  <!-- Stonesbury Logo (Left) -->
  <img src="logo/Stonesbury-logo.png" alt="Stonesbury Logo" class="h-14 w-auto" />

  <!-- Adobe Logo (Right) -->
  <img src="logo/Adobe Express - file.png" alt="Adobe Logo" class="h-14 w-auto" />
</div>

    
    <!-- Login Form Box -->
    <div class="w-full max-w-md mt-20">
      <h2 class="text-3xl font-bold text-gray-900 mb-2">Welcome!</h2>
      <p class="text-sm text-gray-600 mb-6">Please enter your details</p>

      <form id="loginForm" class="space-y-4">
        <div>
          <label class="text-sm font-medium text-gray-700">Email address</label>
          <input type="email" name="email" required
            class="mt-1 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none"
            placeholder="<EMAIL>" />
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700">Password</label>
          <input type="password" name="password" required
            class="mt-1 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none"
            placeholder="••••••••" />
        </div>

        <button type="submit" id="submitBtn"
          class="w-full bg-orange-500 text-white py-2 rounded-md hover:bg-orange-600 transition-all flex items-center justify-center">
          <span id="submitText">Sign in</span>
          <div id="loadingSpinner" class="hidden ml-2">
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
              viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 
                1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        </button>
      </form>
    </div>
  </div>

  <!-- JS for login functionality -->
  <script>
    const validUsers = [
      { email: "<EMAIL>", password: "user1234" },
      { email: "<EMAIL>", password: "user1234" },
      { email: "<EMAIL>", password: "user1234" }
    ];

    document.getElementById('loginForm').addEventListener('submit', function (e) {
      e.preventDefault();
      const email = e.target.email.value.trim();
      const password = e.target.password.value.trim();

      if (!email || !password) {
        alert('Please fill in both fields.');
        return;
      }

      showLoading();

      setTimeout(() => {
        const isValid = validUsers.some(user => user.email === email && user.password === password);

        if (isValid) {
          localStorage.setItem('isAuthenticated', 'true');
          localStorage.setItem('userEmail', email);
          localStorage.setItem('loginTime', new Date().toISOString());
          window.location.href = 'http://localhost:8501?auth=success&email=' + encodeURIComponent(email);
        } else {
          hideLoading();
          alert('Invalid credentials. Please try again.');
        }
      }, 1000);
    });

    function showLoading() {
      document.getElementById('submitBtn').disabled = true;
      document.getElementById('submitBtn').classList.add('opacity-75', 'cursor-not-allowed');
      document.getElementById('submitText').textContent = 'Authenticating...';
      document.getElementById('loadingSpinner').classList.remove('hidden');
    }

    function hideLoading() {
      document.getElementById('submitBtn').disabled = false;
      document.getElementById('submitBtn').classList.remove('opacity-75', 'cursor-not-allowed');
      document.getElementById('submitText').textContent = 'Sign in';
      document.getElementById('loadingSpinner').classList.add('hidden');
    }

    window.addEventListener('load', function () {
      localStorage.removeItem('isAuthenticated');
      localStorage.removeItem('userEmail');
      localStorage.removeItem('loginTime');
    });
  </script>
</body>
</html>
