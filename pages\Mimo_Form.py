import streamlit as st

# Set page config
st.set_page_config(
    page_title="Mimo Form",
    page_icon="📋",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Hide sidebar and ensure white background
st.markdown("""
<style>
    /* Hide sidebar */
    .css-1d391kg {display: none}
    .css-1rs6os {display: none}
    .css-17eq0hr {display: none}

    /* Ensure white background throughout */
    .stApp {
        background-color: white !important;
    }

    .main .block-container {
        background-color: white !important;
        padding: 0 !important;
        margin: 0 !important;
        max-width: 100% !important;
    }

    /* Remove any default margins/padding */
    .element-container {
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Ensure iframe has white background */
    iframe {
        background-color: white !important;
        border: none !important;
    }

    /* Hide Streamlit header and footer */
    header[data-testid="stHeader"] {
        display: none !important;
    }

    .css-18e3th9 {
        padding-top: 0 !important;
    }

    /* Remove any dark backgrounds */
    .css-1d391kg, .css-1rs6os, .css-17eq0hr, .css-1y4p8pa {
        background-color: white !important;
    }
</style>
""", unsafe_allow_html=True)

# Get URL parameters from Streamlit
query_params = st.query_params

# Extract the parameters we need
kpi_id = query_params.get('kpi', '')
category = query_params.get('category', '')
subcategory = query_params.get('subcategory', '')
row = query_params.get('row', '')



# Read and display the sample form HTML
try:
    # Read the Mimo Form.html file
    with open('Mimo Form.html', 'r', encoding='utf-8') as file:
        html_content = file.read()

    # Convert logos to base64 and embed them
    import base64

    def image_to_base64(image_path):
        try:
            with open(image_path, "rb") as img_file:
                return base64.b64encode(img_file.read()).decode()
        except:
            return ""

    # Get base64 encoded logos
    stonesbury_logo = image_to_base64("logo/Stonesbury-logo.png")
    adobe_logo = image_to_base64("logo/Adobe Express - file.png")

    # Replace logo src attributes with base64 data URLs
    if stonesbury_logo:
        html_content = html_content.replace(
            'src="logo/Stonesbury-logo.png"',
            f'src="data:image/png;base64,{stonesbury_logo}"'
        )
    if adobe_logo:
        html_content = html_content.replace(
            'src="logo/Adobe Express - file.png"',
            f'src="data:image/png;base64,{adobe_logo}"'
        )

    # Inject the parameters into the HTML by modifying the JavaScript
    # Add a script to set the form values directly
    import urllib.parse

    # Properly escape the values for JavaScript
    kpi_escaped = kpi_id.replace("'", "\\'").replace('"', '\\"')
    category_escaped = urllib.parse.quote(category) if category else ''
    subcategory_escaped = urllib.parse.quote(subcategory) if subcategory else ''

    injection_script = f"""
    <script>
        // Wait for the page to load, then set the form values
        window.addEventListener('load', function() {{
            setTimeout(function() {{
                try {{
                    // Set KPI ID
                    const kpiField = document.getElementById('kpiId');
                    if (kpiField && '{kpi_escaped}') {{
                        kpiField.value = '{kpi_escaped}';
                        console.log('Set KPI ID:', '{kpi_escaped}');
                    }}

                    // Set Master Category
                    const categoryField = document.getElementById('masterCategory');
                    if (categoryField && '{category_escaped}') {{
                        categoryField.value = decodeURIComponent('{category_escaped}');
                        console.log('Set Master Category:', decodeURIComponent('{category_escaped}'));
                    }}

                    // Set Sub Category
                    const subcategoryField = document.getElementById('subCategory');
                    if (subcategoryField && '{subcategory_escaped}') {{
                        subcategoryField.value = decodeURIComponent('{subcategory_escaped}');
                        console.log('Set Sub Category:', decodeURIComponent('{subcategory_escaped}'));
                    }}

                    console.log('✅ Form successfully prefilled with parameters');
                }} catch (error) {{
                    console.error('❌ Error prefilling form:', error);
                }}
            }}, 200);

            // Fallback: try again after a longer delay
            setTimeout(function() {{
                try {{
                    const kpiField = document.getElementById('kpiId');
                    const categoryField = document.getElementById('masterCategory');
                    const subcategoryField = document.getElementById('subCategory');

                    if (kpiField && !kpiField.value && '{kpi_escaped}') {{
                        kpiField.value = '{kpi_escaped}';
                        console.log('Fallback: Set KPI ID');
                    }}
                    if (categoryField && !categoryField.value && '{category_escaped}') {{
                        categoryField.value = decodeURIComponent('{category_escaped}');
                        console.log('Fallback: Set Master Category');
                    }}
                    if (subcategoryField && !subcategoryField.value && '{subcategory_escaped}') {{
                        subcategoryField.value = decodeURIComponent('{subcategory_escaped}');
                        console.log('Fallback: Set Sub Category');
                    }}
                }} catch (error) {{
                    console.error('❌ Fallback error:', error);
                }}
            }}, 1000);
        }});
    </script>
    """

    # Add additional CSS to ensure proper display
    additional_css = """
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background-color: #f5f5f5;
        }

        .form-container {
            max-width: 100%;
            margin: 0 auto;
            padding: 20px;
        }

        /* Ensure logos display properly */
        .logo-header img {
            max-height: 60px;
            width: auto;
            object-fit: contain;
        }

        /* Ensure form is fully visible */
        .w-full.max-w-6xl {
            max-width: 95%;
            margin: 20px auto;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .md\\:col-span-2 {
                grid-column: span 1;
            }

            .md\\:grid-cols-2 {
                grid-template-columns: 1fr;
            }
        }
    </style>
    """

    # Insert the additional CSS and injection script before the closing </body> tag
    html_content = html_content.replace('</head>', additional_css + '</head>')
    html_content = html_content.replace('</body>', injection_script + '</body>')

    # Display the HTML content with full height and no scrolling issues
    st.components.v1.html(html_content, height=1200, scrolling=True)

except FileNotFoundError:
    st.error("❌ Mimo form file not found!")
    st.info("Please ensure 'Mimo Form.html' exists in the project directory.")
except Exception as e:
    st.error(f"❌ Error loading Mimo form: {str(e)}")
